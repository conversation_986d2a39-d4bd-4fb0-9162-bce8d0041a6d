# Testing Reasoning/Thinking Functionality

## What Was Fixed

1. **OpenRouter DeepSeek Configuration**: Added `reasoning: true` to provider options for OpenRouter DeepSeek models
2. **Enhanced Stream Processing**: Improved detection of reasoning stream parts
3. **Content Extraction**: Added comprehensive content extraction from multiple possible fields
4. **Database Storage**: Removed user preference check that was blocking reasoning storage
5. **Debugging**: Added detailed logging to track reasoning capture

## How to Test

### 1. Check Console Logs
When using a reasoning model (like OpenRouter DeepSeek R1), you should see these logs:

```
[OpenRouter] Configuring reasoning for DeepSeek model: deepseek-reasoner
[Reasoning Debug] Enabling reasoning tokens for model: deepseek-reasoner
[OpenRouter Reasoning Debug] Using OpenRouter with reasoning model: deepseek-reasoner
[OpenRouter DeepSeek Debug] Configuring OpenRouter DeepSeek for reasoning: deepseek-reasoner
[OpenRouter DeepSeek Debug] Added reasoning=true to provider options
```

### 2. Stream Processing Logs
During streaming, look for:

```
[Stream Debug] Received part type: reasoning
[Reasoning Stream Debug] Found reasoning part: {...}
[Reasoning Debug] Captured reasoning content (reasoning): ...
[Reasoning Debug] Total accumulated reasoning: X chars
[Reasoning Debug] Storing thinking in DB: ...
```

### 3. UI Display
- Make sure "Show Thinking" is enabled in preferences (should be default)
- Look for the orange "Thought Process" section above the main response
- The reasoning should appear in real-time as the model thinks

### 4. Test Models
Try these reasoning-capable models:
- OpenRouter: `deepseek/deepseek-r1`
- OpenAI: `o1-mini`, `o1-preview`
- Anthropic: `claude-4-opus-20250514` (with thinking enabled)

### 5. Test Prompts
Use prompts that require reasoning:
- "Solve this step by step: If a train leaves Chicago at 2 PM traveling 60 mph..."
- "Explain your reasoning for why the sky is blue"
- "Think through the pros and cons of remote work"

## Expected Behavior

1. **Console**: Should see detailed reasoning debug logs
2. **UI**: Should display thinking content in orange section
3. **Database**: Message should have `thinking` field populated
4. **Real-time**: Reasoning should stream in real-time, not just appear at the end

## If Still Not Working

Check these potential issues:

1. **Model Support**: Verify the model actually supports reasoning
2. **API Keys**: Ensure you have valid API keys for the provider
3. **User Preferences**: Check that `showThinking` is `true` in preferences
4. **Browser Console**: Look for any JavaScript errors in the browser
5. **Network**: Check if reasoning tokens are being sent by the provider

## Debug Commands

In browser console, check user preferences:
```javascript
// This should show showThinking: true
console.log(await convex.query("preferences:get"));
```

Check if a message has thinking content:
```javascript
// Replace MESSAGE_ID with actual message ID
console.log(await convex.query("messages:get", { id: "MESSAGE_ID" }));
```
