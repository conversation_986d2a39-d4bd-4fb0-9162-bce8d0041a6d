import { v } from "convex/values";
import { query, mutation, internalQuery } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { paginationOptsValidator } from "convex/server";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

export const list = query({
  args: {
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
    paginationOpts: paginationOptsValidator,
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify user owns the conversation
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    const branchId = args.branchId || conversation.currentBranch || "main";

    // Get messages for the specific branch only - no fallback
    const branchMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", args.conversationId).eq("branchId", branchId)
      )
      .order("asc")
      .collect();

    // Paginate the results manually
    const startIndex = args.paginationOpts.cursor
      ? parseInt(args.paginationOpts.cursor)
      : 0;
    const endIndex = startIndex + args.paginationOpts.numItems;
    const page = branchMessages.slice(startIndex, endIndex);
    const hasMore = endIndex < branchMessages.length;

    return {
      page,
      isDone: !hasMore,
      continueCursor: hasMore ? endIndex.toString() : null,
    };
  },
});

export const add = mutation({
  args: {
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
    parentMessageId: v.optional(v.id("messages")),
    role: v.union(
      v.literal("user"),
      v.literal("assistant"),
      v.literal("system"),
      v.literal("tool")
    ),
    content: v.string(),
    thinking: v.optional(v.string()),
    attachments: v.optional(
      v.array(
        v.object({
          type: v.union(
            v.literal("image"),
            v.literal("file"),
            v.literal("audio"),
            v.literal("video")
          ),
          url: v.string(),
          name: v.string(),
          size: v.optional(v.number()),
          storageId: v.optional(v.id("_storage")),
          extractedText: v.optional(v.string()),
          mimeType: v.optional(v.string()),
        })
      )
    ),
    toolCalls: v.optional(
      v.array(
        v.object({
          id: v.string(),
          name: v.string(),
          arguments: v.string(),
          result: v.optional(v.string()),
          startTime: v.optional(v.number()),
          endTime: v.optional(v.number()),
        })
      )
    ),
    toolCallId: v.optional(v.string()),
    contentSequence: v.optional(
      v.array(
        v.union(
          v.object({
            type: v.literal("content"),
            text: v.string(),
            timestamp: v.number(),
          }),
          v.object({
            type: v.literal("tool"),
            toolCallId: v.string(),
            timestamp: v.number(),
          })
        )
      )
    ),
    generationMetrics: v.optional(
      v.object({
        provider: v.string(),
        model: v.string(),
        tokensUsed: v.optional(v.number()),
        inputTokens: v.optional(v.number()),
        outputTokens: v.optional(v.number()),
        generationTimeMs: v.optional(v.number()),
        tokensPerSecond: v.optional(v.number()),
        finishReason: v.optional(v.string()),
        temperature: v.optional(v.number()),
        maxOutputTokens: v.optional(v.number()),
        timeToFirstTokenMs: v.optional(v.number()),
        timeToFirstContentMs: v.optional(v.number()),
        reasoningTimeMs: v.optional(v.number()),
        toolExecutionTimeMs: v.optional(v.number()),
        attemptedProviders: v.optional(v.array(v.string())),
        fallbackUsed: v.optional(v.boolean()),
      })
    ),
    isError: v.optional(v.boolean()),
    canvasData: v.optional(
      v.union(
        v.object({
          type: v.union(
            v.literal("markdown"),
            v.literal("code"),
            v.literal("chart"),
            v.literal("react")
          ),
          title: v.string(),
          content: v.string(),
          language: v.optional(v.string()),
          chartSpec: v.optional(v.string()),
          library: v.optional(
            v.union(v.literal("chartjs"), v.literal("echarts"), v.literal("d3"))
          ),
          updatedAt: v.number(),
        }),
        v.array(
          v.object({
            type: v.union(
              v.literal("markdown"),
              v.literal("code"),
              v.literal("chart"),
              v.literal("react")
            ),
            title: v.string(),
            content: v.string(),
            language: v.optional(v.string()),
            chartSpec: v.optional(v.string()),
            library: v.optional(
              v.union(
                v.literal("chartjs"),
                v.literal("echarts"),
                v.literal("d3")
              )
            ),
            updatedAt: v.number(),
          })
        )
      )
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify user owns the conversation
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    const branchId = args.branchId || conversation.currentBranch || "main";

    // Update conversation's last message time
    await ctx.db.patch(args.conversationId, {
      lastMessageAt: Date.now(),
    });

    const newMessageId = await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      branchId,
      parentMessageId: args.parentMessageId,
      role: args.role,
      content: args.content,
      thinking: args.thinking,
      attachments: args.attachments,
      toolCalls: args.toolCalls,
      toolCallId: args.toolCallId,
      contentSequence: args.contentSequence,
      generationMetrics: args.generationMetrics,
      isError: args.isError,
      canvasData: args.canvasData,
    });

    // Update branch counters
    const branch = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", args.conversationId).eq("branchId", branchId)
      )
      .unique();

    if (branch) {
      const newCount = (branch.messageCount ?? 0) + 1;
      const now = Date.now();
      await ctx.db.patch(branch._id, {
        messageCount: newCount,
        lastMessageAt: now,
      });

      // If this is the current branch, update conversation
      if (branchId === conversation.currentBranch) {
        await ctx.db.patch(args.conversationId, {
          messageCount: newCount,
          lastMessageAt: now,
        });
      }
    }

    // Schedule background indexing into Cloudflare Vectorize (non-blocking)
    try {
      await ctx.scheduler.runAfter(0, internal.vectorize.indexMessage, {
        messageId: newMessageId,
        conversationId: args.conversationId,
        userId,
        branchId,
        text: args.content,
      });
    } catch (err) {
      // Log but don't fail the mutation if scheduling fails
      console.error("Failed to schedule Vectorize indexing:", err);
    }

    return newMessageId;
  },
});

export const update = mutation({
  args: {
    messageId: v.id("messages"),
    content: v.string(),
    thinking: v.optional(v.string()),
    toolCalls: v.optional(
      v.array(
        v.object({
          id: v.string(),
          name: v.string(),
          arguments: v.string(),
          result: v.optional(v.string()),
          startTime: v.optional(v.number()),
          endTime: v.optional(v.number()),
        })
      )
    ),
    contentSequence: v.optional(
      v.array(
        v.union(
          v.object({
            type: v.literal("content"),
            text: v.string(),
            timestamp: v.number(),
          }),
          v.object({
            type: v.literal("tool"),
            toolCallId: v.string(),
            timestamp: v.number(),
          })
        )
      )
    ),
    generationMetrics: v.optional(
      v.object({
        provider: v.string(),
        model: v.string(),
        tokensUsed: v.optional(v.number()),
        inputTokens: v.optional(v.number()),
        outputTokens: v.optional(v.number()),
        generationTimeMs: v.optional(v.number()),
        tokensPerSecond: v.optional(v.number()),
        finishReason: v.optional(v.string()),
        temperature: v.optional(v.number()),
        maxOutputTokens: v.optional(v.number()),
        timeToFirstTokenMs: v.optional(v.number()),
        timeToFirstContentMs: v.optional(v.number()),
        reasoningTimeMs: v.optional(v.number()),
        toolExecutionTimeMs: v.optional(v.number()),
        attemptedProviders: v.optional(v.array(v.string())),
        fallbackUsed: v.optional(v.boolean()),
      })
    ),
    isError: v.optional(v.boolean()),
    canvasData: v.optional(
      v.union(
        v.object({
          type: v.union(
            v.literal("markdown"),
            v.literal("code"),
            v.literal("chart"),
            v.literal("react")
          ),
          title: v.string(),
          content: v.string(),
          language: v.optional(v.string()),
          chartSpec: v.optional(v.string()),
          library: v.optional(
            v.union(v.literal("chartjs"), v.literal("echarts"), v.literal("d3"))
          ),
          updatedAt: v.number(),
        }),
        v.array(
          v.object({
            type: v.union(
              v.literal("markdown"),
              v.literal("code"),
              v.literal("chart"),
              v.literal("react")
            ),
            title: v.string(),
            content: v.string(),
            language: v.optional(v.string()),
            chartSpec: v.optional(v.string()),
            library: v.optional(
              v.union(
                v.literal("chartjs"),
                v.literal("echarts"),
                v.literal("d3")
              )
            ),
            updatedAt: v.number(),
          })
        )
      )
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get the message and verify ownership through conversation
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    const conversation = await ctx.db.get(message.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Not authorized to update this message");
    }

    // If no new canvas provided, just update other fields and exit
    if (!args.canvasData) {
      const updateObj: any = { content: args.content };
      if (args.thinking !== undefined) updateObj.thinking = args.thinking;
      if (args.toolCalls) updateObj.toolCalls = args.toolCalls;
      if (args.contentSequence)
        updateObj.contentSequence = args.contentSequence;
      if (args.generationMetrics)
        updateObj.generationMetrics = args.generationMetrics;
      if (args.isError !== undefined) updateObj.isError = args.isError;
      await ctx.db.patch(args.messageId, updateObj);
      return;
    }

    // Fetch existing canvasData and merge/append appropriately
    const existing = message.canvasData as any;

    if (Array.isArray(existing)) {
      const updatedArray = [...existing, args.canvasData];
      await ctx.db.patch(args.messageId, { canvasData: updatedArray });
    } else if (existing) {
      const canvases = [existing, args.canvasData];
      await ctx.db.patch(args.messageId, { canvasData: canvases });
    } else {
      await ctx.db.patch(args.messageId, {
        canvasData: [args.canvasData] as any,
      });
    }
  },
});

export const cleanupOldToolMessages = mutation({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    // Get all tool messages for this conversation
    const toolMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .filter((q) => q.eq(q.field("role"), "tool"))
      .collect();

    // Delete tool messages that look malformed (old format)
    const deletePromises = toolMessages
      .filter(
        (msg) =>
          // Delete if content starts with "Tool output for" (old format)
          msg.content.startsWith("Tool output for") ||
          // Or if it's just the tool call ID
          msg.content.match(/^[a-zA-Z0-9]+$/) ||
          // Or if it's empty
          !msg.content.trim()
      )
      .map((msg) => ctx.db.delete(msg._id));

    await Promise.all(deletePromises);

    return { cleaned: deletePromises.length };
  },
});

export const editMessage = mutation({
  args: {
    messageId: v.id("messages"),
    newContent: v.string(),
    branchTitle: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get the original message
    const originalMessage = await ctx.db.get(args.messageId);
    if (!originalMessage) {
      throw new Error("Message not found");
    }

    // Verify ownership
    const conversation = await ctx.db.get(originalMessage.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Not authorized to edit this message");
    }

    // Create a new branch
    const branchId = `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const branchTitle =
      args.branchTitle || `Edit: ${args.newContent.slice(0, 30)}...`;

    await ctx.db.insert("conversationBranches", {
      conversationId: originalMessage.conversationId,
      branchId,
      parentBranchId: originalMessage.branchId || "main",
      branchPoint: args.messageId,
      title: branchTitle,
      createdAt: Date.now(),
      isActive: true,
    });

    // Set all other branches as inactive
    const existingBranches = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", originalMessage.conversationId)
      )
      .collect();

    for (const branch of existingBranches) {
      if (branch.branchId !== branchId) {
        await ctx.db.patch(branch._id, { isActive: false });
      }
    }

    // Update conversation's current branch
    await ctx.db.patch(originalMessage.conversationId, {
      currentBranch: branchId,
    });

    // Get the current branch to copy messages from
    const currentBranchId =
      originalMessage.branchId || conversation.currentBranch || "main";

    // Copy messages from the current branch only, up to the edit point
    const branchMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q
          .eq("conversationId", originalMessage.conversationId)
          .eq("branchId", currentBranchId)
      )
      .collect();

    const sortedMessages = branchMessages.sort(
      (a, b) => a._creationTime - b._creationTime
    );
    const editPointIndex = sortedMessages.findIndex(
      (m) => m._id === args.messageId
    );

    if (editPointIndex === -1) {
      throw new Error("Message not found in current branch");
    }

    const messagesToCopy = sortedMessages.slice(0, editPointIndex);

    // Copy messages to new branch
    for (const message of messagesToCopy) {
      await ctx.db.insert("messages", {
        conversationId: message.conversationId,
        branchId,
        role: message.role,
        content: message.content,
        thinking: message.thinking,
        attachments: message.attachments,
        toolCalls: message.toolCalls,
        toolCallId: message.toolCallId,
        generationMetrics: message.generationMetrics,
        canvasData: message.canvasData,
      });
    }

    // Add the edited message to the new branch
    const editedMessageId = await ctx.db.insert("messages", {
      conversationId: originalMessage.conversationId,
      branchId,
      parentMessageId: args.messageId,
      role: originalMessage.role,
      content: args.newContent,
      attachments: originalMessage.attachments,
      isEdited: true,
      editedAt: Date.now(),
    });

    const count = messagesToCopy.length + 1;
    const lastTime = Date.now();
    const newBranchDoc = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation_branch", (q) =>
        q
          .eq("conversationId", originalMessage.conversationId)
          .eq("branchId", branchId)
      )
      .unique();
    if (newBranchDoc) {
      await ctx.db.patch(newBranchDoc._id, {
        messageCount: count,
        lastMessageAt: lastTime,
      });
    }
    await ctx.db.patch(originalMessage.conversationId, {
      messageCount: count,
      lastMessageAt: lastTime,
    });

    return { branchId, messageId: editedMessageId };
  },
});

export const retryMessage = mutation({
  args: {
    messageId: v.id("messages"),
    branchTitle: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get the message to retry
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    // Verify ownership
    const conversation = await ctx.db.get(message.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Not authorized to retry this message");
    }

    // Only allow retrying assistant messages
    if (message.role !== "assistant") {
      throw new Error("Can only retry assistant messages");
    }

    // Create a new branch for the retry
    const branchId = `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const branchTitle =
      args.branchTitle || `Retry: ${message.content.slice(0, 30)}...`;

    await ctx.db.insert("conversationBranches", {
      conversationId: message.conversationId,
      branchId,
      parentBranchId: message.branchId || "main",
      branchPoint: args.messageId,
      title: branchTitle,
      createdAt: Date.now(),
      isActive: true,
    });

    // Set all other branches as inactive
    const existingBranches = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", message.conversationId)
      )
      .collect();

    for (const branch of existingBranches) {
      if (branch.branchId !== branchId) {
        await ctx.db.patch(branch._id, { isActive: false });
      }
    }

    // Update conversation's current branch
    await ctx.db.patch(message.conversationId, { currentBranch: branchId });

    // Get the current branch to copy messages from
    const currentBranchId =
      message.branchId || conversation.currentBranch || "main";

    // Copy messages from the current branch only, up to (but not including) the message to retry
    const branchMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q
          .eq("conversationId", message.conversationId)
          .eq("branchId", currentBranchId)
      )
      .collect();

    const sortedMessages = branchMessages.sort(
      (a, b) => a._creationTime - b._creationTime
    );
    const retryPointIndex = sortedMessages.findIndex(
      (m) => m._id === args.messageId
    );

    if (retryPointIndex === -1) {
      throw new Error("Message not found in current branch");
    }

    const messagesToCopy = sortedMessages.slice(0, retryPointIndex);

    // Copy messages to new branch
    for (const msg of messagesToCopy) {
      await ctx.db.insert("messages", {
        conversationId: msg.conversationId,
        branchId,
        role: msg.role,
        content: msg.content,
        thinking: msg.thinking,
        attachments: msg.attachments,
        toolCalls: msg.toolCalls,
        toolCallId: msg.toolCallId,
        generationMetrics: msg.generationMetrics,
        canvasData: msg.canvasData,
      });
    }

    const count = messagesToCopy.length;
    const lastTime =
      count > 0
        ? Math.max(...messagesToCopy.map((m) => m._creationTime))
        : Date.now();
    const newBranchDoc = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", message.conversationId).eq("branchId", branchId)
      )
      .unique();
    if (newBranchDoc) {
      await ctx.db.patch(newBranchDoc._id, {
        messageCount: count,
        lastMessageAt: lastTime,
      });
    }
    await ctx.db.patch(message.conversationId, {
      messageCount: count,
      lastMessageAt: lastTime,
    });

    return { branchId, needsRegeneration: true };
  },
});

export const copyMessage = mutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    // Verify ownership
    const conversation = await ctx.db.get(message.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Not authorized to copy this message");
    }

    return { content: message.content };
  },
});

// Migration function to assign existing messages to main branch
export const migrateMessagesToMainBranch = mutation({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    let mainBranch: any = null;

    // Verify user owns the conversation
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Find messages without branchId
    const unbrandedMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .filter((q) => q.eq(q.field("branchId"), undefined))
      .collect();

    // Ensure main branch exists
    mainBranch = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", args.conversationId).eq("branchId", "main")
      )
      .unique();

    if (!mainBranch) {
      const now = Date.now();
      const insertedId = await ctx.db.insert("conversationBranches", {
        conversationId: args.conversationId,
        branchId: "main",
        title: "Main",
        createdAt: now,
        isActive: true,
        messageCount: 0,
        lastMessageAt: now,
      });
      mainBranch = await ctx.db.get(insertedId);
      if (!mainBranch) throw new Error("Failed to create main branch");
    }

    // Update conversation to have main as current branch if not set
    if (!conversation.currentBranch) {
      await ctx.db.patch(args.conversationId, { currentBranch: "main" });
    }

    // Assign all unbranded messages to main branch
    for (const message of unbrandedMessages) {
      await ctx.db.patch(message._id, { branchId: "main" });
    }

    // Update counts
    const migratedMessages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", args.conversationId).eq("branchId", "main")
      )
      .collect();

    const count = migratedMessages.length;
    const lastTime =
      count > 0
        ? Math.max(...migratedMessages.map((m) => m._creationTime))
        : mainBranch.createdAt;

    await ctx.db.patch(mainBranch._id, {
      messageCount: count,
      lastMessageAt: lastTime,
    });

    await ctx.db.patch(args.conversationId, {
      messageCount: count,
      lastMessageAt: lastTime,
    });

    return { migratedCount: unbrandedMessages.length };
  },
});

export const updateCanvas = mutation({
  args: {
    messageId: v.id("messages"),
    canvasData: v.object({
      type: v.union(
        v.literal("markdown"),
        v.literal("code"),
        v.literal("chart"),
        v.literal("react")
      ),
      title: v.string(),
      content: v.string(),
      language: v.optional(v.string()),
      chartSpec: v.optional(v.string()),
      library: v.optional(
        v.union(v.literal("chartjs"), v.literal("echarts"), v.literal("d3"))
      ),
      updatedAt: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get the message and verify ownership through conversation
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    const conversation = await ctx.db.get(message.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Not authorized to update this message");
    }

    // Fetch existing canvasData and merge/append appropriately
    const existing = message.canvasData as any;

    if (Array.isArray(existing)) {
      // Push the new canvas data to the array
      const updatedArray = [...existing, args.canvasData];
      await ctx.db.patch(args.messageId, {
        canvasData: updatedArray,
      });
    } else if (existing) {
      // Convert legacy single canvas to array, keeping history then add/replace
      const canvases = [existing, args.canvasData];
      await ctx.db.patch(args.messageId, {
        canvasData: canvases as any,
      });
    } else {
      // First canvas for this message
      await ctx.db.patch(args.messageId, {
        canvasData: [args.canvasData] as any,
      });
    }
  },
});

// Quickly fetch message snippets (id, role, content) for an array of message IDs
export const getSnippets = internalQuery({
  args: {
    ids: v.array(v.id("messages")),
  },
  returns: v.array(
    v.object({
      _id: v.id("messages"),
      role: v.string(),
      content: v.string(),
    })
  ),
  handler: async (ctx, args) => {
    const snippets: Array<{
      _id: Id<"messages">;
      role: string;
      content: string;
    }> = [];
    for (const mid of args.ids) {
      const doc = await ctx.db.get(mid);
      if (doc) {
        snippets.push({ _id: doc._id, role: doc.role, content: doc.content });
      }
    }
    return snippets;
  },
});

export const getUsageAnalytics = query({
  args: {
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const days = args.days || 30;
    const cutoffTime = Date.now() - days * 24 * 60 * 60 * 1000;

    // Get all conversations for the user
    const userConversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const conversationIds = userConversations.map((c) => c._id);

    // Get all messages with generation metrics from user's conversations within the time period
    const messagesWithMetrics = [];
    for (const conversationId of conversationIds) {
      const messages = await ctx.db
        .query("messages")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversationId)
        )
        .filter((q) =>
          q.and(
            q.gte(q.field("_creationTime"), cutoffTime),
            q.neq(q.field("generationMetrics"), undefined)
          )
        )
        .collect();
      messagesWithMetrics.push(...messages);
    }

    // Process model breakdown
    const modelBreakdown = new Map<
      string,
      {
        model: string;
        provider: string;
        totalTokens: number;
        messageCount: number;
        inputTokens: number;
        outputTokens: number;
      }
    >();

    // Process provider breakdown
    const providerBreakdown = new Map<
      string,
      {
        provider: string;
        totalTokens: number;
        messageCount: number;
        inputTokens: number;
        outputTokens: number;
      }
    >();

    // Process daily usage trends
    const dailyUsage = new Map<
      string,
      {
        date: string;
        totalTokens: number;
        messageCount: number;
        inputTokens: number;
        outputTokens: number;
      }
    >();

    for (const message of messagesWithMetrics) {
      const metrics = message.generationMetrics;
      if (!metrics) continue;

      const { provider, model, tokensUsed, inputTokens, outputTokens } =
        metrics;
      const tokens = tokensUsed || (inputTokens || 0) + (outputTokens || 0);
      const prompt = inputTokens || 0;
      const completion = outputTokens || 0;

      // Model breakdown
      const modelKey = `${provider}-${model}`;
      if (modelBreakdown.has(modelKey)) {
        const existing = modelBreakdown.get(modelKey)!;
        existing.totalTokens += tokens;
        existing.messageCount += 1;
        existing.inputTokens += prompt;
        existing.outputTokens += completion;
      } else {
        modelBreakdown.set(modelKey, {
          model,
          provider,
          totalTokens: tokens,
          messageCount: 1,
          inputTokens: prompt,
          outputTokens: completion,
        });
      }

      // Provider breakdown
      if (providerBreakdown.has(provider)) {
        const existing = providerBreakdown.get(provider)!;
        existing.totalTokens += tokens;
        existing.messageCount += 1;
        existing.inputTokens += prompt;
        existing.outputTokens += completion;
      } else {
        providerBreakdown.set(provider, {
          provider,
          totalTokens: tokens,
          messageCount: 1,
          inputTokens: prompt,
          outputTokens: completion,
        });
      }

      // Daily usage trends
      const date = new Date(message._creationTime).toISOString().split("T")[0];
      if (dailyUsage.has(date)) {
        const existing = dailyUsage.get(date)!;
        existing.totalTokens += tokens;
        existing.messageCount += 1;
        existing.inputTokens += prompt;
        existing.outputTokens += completion;
      } else {
        dailyUsage.set(date, {
          date,
          totalTokens: tokens,
          messageCount: 1,
          inputTokens: prompt,
          outputTokens: completion,
        });
      }
    }

    // Convert maps to arrays and sort
    const modelBreakdownArray = Array.from(modelBreakdown.values()).sort(
      (a, b) => b.totalTokens - a.totalTokens
    );

    const providerBreakdownArray = Array.from(providerBreakdown.values()).sort(
      (a, b) => b.totalTokens - a.totalTokens
    );

    const dailyUsageArray = Array.from(dailyUsage.values()).sort((a, b) =>
      a.date.localeCompare(b.date)
    );

    // Calculate totals
    const totalTokens = modelBreakdownArray.reduce(
      (sum, item) => sum + item.totalTokens,
      0
    );
    const totalMessages = modelBreakdownArray.reduce(
      (sum, item) => sum + item.messageCount,
      0
    );
    const totalPromptTokens = modelBreakdownArray.reduce(
      (sum, item) => sum + item.inputTokens,
      0
    );
    const totalCompletionTokens = modelBreakdownArray.reduce(
      (sum, item) => sum + item.outputTokens,
      0
    );

    return {
      modelBreakdown: modelBreakdownArray,
      providerBreakdown: providerBreakdownArray,
      dailyUsage: dailyUsageArray,
      totals: {
        totalTokens,
        totalMessages,
        totalPromptTokens,
        totalCompletionTokens,
      },
      timeRange: {
        days,
        startDate: new Date(cutoffTime).toISOString(),
        endDate: new Date().toISOString(),
      },
    };
  },
});

export const prepareAssistantMessage = mutation({
  args: {
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    const branchId = args.branchId || conversation.currentBranch || "main";

    // Clear cancellation and generation state
    await ctx.db.patch(args.conversationId, {
      isGenerationCancelled: false,
      isGenerating: false,
      generatingMessageId: undefined,
    });

    // Insert empty assistant message
    const messageId = await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      branchId,
      role: "assistant",
      content: "",
    });

    // Set generation state
    await ctx.db.patch(args.conversationId, {
      isGenerating: true,
      generatingMessageId: messageId,
    });

    return messageId;
  },
});
