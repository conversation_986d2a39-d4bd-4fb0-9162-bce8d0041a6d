import { internalMutation, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Call this after user registration to send welcome email
export const sendWelcomeEmailAfterRegistration = internalAction({
  args: {
    userId: v.id("users"),
    email: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      // Generate a verification URL (you'd implement proper token generation)
      const verificationUrl = `https://ai.erzen.tk/verify?email=${encodeURIComponent(args.email)}`;

      await ctx.runAction(internal.email.sendVerificationEmail, {
        email: args.email,
        verificationUrl,
      });

      console.log(
        `Welcome email sent to ${args.email} for user ${args.userId}`
      );
    } catch (error) {
      console.error("Failed to send welcome email after registration:", error);
      // Don't throw - registration should succeed even if email fails
    }

    return null;
  },
});
