import {
  action,
  internalMutation,
  internalQuery,
  query,
} from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";

// ---------------- Internal helpers -----------------

export const getBranchMeta = internalQuery({
  args: {
    repoFullName: v.string(),
    branch: v.string(),
  },
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id("repoBranches"),
      commitSha: v.string(),
    })
  ),
  handler: async (ctx, args) => {
    const meta = await ctx.db
      .query("repoBranches")
      .withIndex("by_repo_branch", (q) =>
        q.eq("repoFullName", args.repoFullName).eq("branch", args.branch)
      )
      .unique();
    if (!meta) return null;
    return { _id: meta._id, commitSha: meta.commitSha } as const;
  },
});

export const upsertBranchMeta = internalMutation({
  args: {
    repoFullName: v.string(),
    branch: v.string(),
    commitSha: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("repoBranches")
      .withIndex("by_repo_branch", (q) =>
        q.eq("repoFullName", args.repoFullName).eq("branch", args.branch)
      )
      .unique();
    const userId = await getAuthUserId(ctx);
    if (existing) {
      await ctx.db.replace(existing._id, {
        ...existing,
        commitSha: args.commitSha,
        fetchedAt: Date.now(),
      });
    } else {
      await ctx.db.insert("repoBranches", {
        userId: userId ?? undefined,
        repoFullName: args.repoFullName,
        branch: args.branch,
        commitSha: args.commitSha,
        fetchedAt: Date.now(),
      });
    }
    return null;
  },
});

export const upsertFile = internalMutation({
  args: {
    repoFullName: v.string(),
    branch: v.string(),
    path: v.string(),
    sha: v.string(),
    size: v.number(),
    content: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const existingFile = await ctx.db
      .query("repoFiles")
      .withIndex("by_repo_branch_path", (q) =>
        q
          .eq("repoFullName", args.repoFullName)
          .eq("branch", args.branch)
          .eq("path", args.path)
      )
      .unique();
    if (existingFile && existingFile.sha === args.sha) return null;
    if (existingFile) {
      await ctx.db.replace(existingFile._id, {
        ...existingFile,
        sha: args.sha,
        size: args.size,
        content: args.content,
        fetchedAt: Date.now(),
      });
    } else {
      await ctx.db.insert("repoFiles", {
        repoFullName: args.repoFullName,
        branch: args.branch,
        path: args.path,
        sha: args.sha,
        size: args.size,
        content: args.content,
        fetchedAt: Date.now(),
      });
    }
    return null;
  },
});

// ---------------- Public action -----------------

export const listCachedRepos = query({
  args: {},
  returns: v.array(
    v.object({
      repoFullName: v.string(),
      branch: v.string(),
      commitSha: v.string(),
      fetchedAt: v.number(),
    })
  ),
  handler: async (ctx) => {
    const metas = await ctx.db.query("repoBranches").order("desc").take(100);
    return metas.map((m) => ({
      repoFullName: m.repoFullName,
      branch: m.branch,
      commitSha: m.commitSha,
      fetchedAt: m.fetchedAt,
    }));
  },
});
