"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";
import { validateN8nConnection, fetchN8nWorkflows } from "./ai/tools/n8n";

// Test connection to n8n
export const testConnection = action({
  args: {
    apiUrl: v.string(),
    apiKey: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const isValid = await validateN8nConnection(args.apiUrl, args.apiKey);

    if (isValid) {
      // Fetch available workflows
      const workflows = await fetchN8nWorkflows(args.apiUrl, args.apiKey);
      return { success: true, workflows };
    }

    return { success: false, message: "Failed to connect to n8n" };
  },
});
