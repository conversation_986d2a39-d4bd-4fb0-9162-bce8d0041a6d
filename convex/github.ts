import {
  query,
  action,
  internalMutation,
  internalQuery,
} from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { Octokit } from "octokit";

/**
 * Internal mutation to store OAuth credentials once the user has linked their GitHub account.
 */
export const storeCredentials = internalMutation({
  args: {
    userId: v.id("users"),
    githubId: v.string(),
    username: v.string(),
    accessToken: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Upsert record (one per user)
    const existing = await ctx.db
      .query("githubAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (existing) {
      await ctx.db.replace(existing._id, {
        ...existing,
        githubId: args.githubId,
        username: args.username,
        accessToken: args.accessToken,
        linkedAt: Date.now(),
      });
    } else {
      await ctx.db.insert("githubAccounts", {
        userId: args.userId,
        githubId: args.githubId,
        username: args.username,
        accessToken: args.accessToken,
        linkedAt: Date.now(),
      });
    }

    return null;
  },
});

// Internal query to fetch stored GitHub account for the current user (token only)
export const getAccount = internalQuery({
  args: {},
  returns: v.union(
    v.null(),
    v.object({
      accessToken: v.string(),
    })
  ),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    const account = await ctx.db
      .query("githubAccounts")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .unique();
    if (!account) return null;
    return { accessToken: account.accessToken } as const;
  },
});

// Public query – simple boolean to tell front-end if GitHub is linked.
export const isLinked = query({
  args: {},
  returns: v.boolean(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return false;
    const account = await ctx.db
      .query("githubAccounts")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .unique();
    return !!account;
  },
});

/**
 * Public action to list repositories for the authenticated user.
 * Requires that GitHub has already been linked.
 */
export const listRepositories = action({
  args: {},
  returns: v.array(
    v.object({
      id: v.number(),
      name: v.string(),
      full_name: v.string(),
      private: v.boolean(),
      html_url: v.string(),
    })
  ),
  handler: async (ctx) => {
    const account: { accessToken: string } | null = await ctx.runQuery(
      internal.github.getAccount,
      {}
    );
    if (!account) throw new Error("GitHub account not linked");

    const octokit = new Octokit({ auth: account.accessToken });

    // The Octokit type definitions are large; for brevity we treat the repo list as any[] here.
    const { data } = (await octokit.rest.repos.listForAuthenticatedUser({
      per_page: 100,
    })) as { data: any[] };

    return data.map((repo: any) => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name,
      private: repo.private,
      html_url: repo.html_url,
    }));
  },
});

export const startOAuth = action({
  args: {
    redirectUri: v.optional(v.string()),
  },
  returns: v.string(),
  handler: async (ctx, args) => {
    const clientId = process.env.GITHUB_CLIENT_ID;
    if (!clientId) throw new Error("GITHUB_CLIENT_ID not configured");
    const redirectUri =
      args.redirectUri ??
      (process.env.CONVEX_SITE_URL || "http://localhost:5173") +
        "/github/oauth/callback";
    const scope = "repo read:user";
    return `https://github.com/login/oauth/authorize?client_id=${clientId}&scope=${encodeURIComponent(
      scope
    )}&redirect_uri=${encodeURIComponent(redirectUri)}`;
  },
});
