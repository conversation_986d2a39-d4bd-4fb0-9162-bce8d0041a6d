"use node";

import { v } from "convex/values";
import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import Strip<PERSON> from "stripe";

// Initialize Stripe lazily to avoid issues during deployment
function getStripe() {
  return new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: "2025-07-30.basil",
  });
}

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export const processStripeEvent = internalAction({
  args: {
    body: v.string(),
    signature: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
  }),
  handler: async (ctx, args) => {
    let event: Stripe.Event;

    try {
      const stripe = getStripe();
      event = stripe.webhooks.constructEvent(
        args.body,
        args.signature,
        endpointSecret
      );
    } catch (err) {
      console.error("Webhook signature verification failed:", err);
      return { success: false, message: `Webhook Error: ${String(err)}` };
    }

    // Use idempotent processing - try to record and process in one operation
    try {
      const result = await ctx.runMutation(
        internal.stripeHelpers.processWebhookEventIdempotent,
        {
          stripeEventId: event.id,
          eventType: event.type,
          eventData: JSON.stringify(event.data.object),
        }
      );

      if (result.alreadyProcessed) {
        console.log(`Event ${event.id} already processed`);
        return { success: true, message: "Event already processed" };
      }

      // Process the specific event type
      switch (event.type) {
        case "checkout.session.completed": {
          const session = event.data.object;
          await handleCheckoutSessionCompleted(ctx, session);
          break;
        }

        case "customer.subscription.created":
        case "customer.subscription.updated": {
          const subscription = event.data.object;
          await handleSubscriptionUpdated(ctx, subscription);
          break;
        }

        case "customer.subscription.deleted": {
          const subscription = event.data.object;
          await handleSubscriptionDeleted(ctx, subscription);
          break;
        }

        case "invoice.payment_succeeded": {
          const invoice = event.data.object;
          await handleInvoicePaymentSucceeded(ctx, invoice);
          break;
        }

        case "invoice.payment_failed": {
          const invoice = event.data.object;
          await handleInvoicePaymentFailed(ctx, invoice);
          break;
        }

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      // Mark event as processed
      await ctx.runMutation(internal.stripeHelpers.markEventProcessed, {
        stripeEventId: event.id,
      });

      return { success: true, message: "Webhook processed successfully" };
    } catch (error) {
      console.error(`Error processing webhook ${event.id}:`, error);

      // If it's a concurrency error, return success to avoid retries
      if (String(error).includes("OptimisticConcurrencyControlFailure")) {
        console.log(
          `Concurrency conflict for event ${event.id}, likely processed by another instance`
        );
        return { success: true, message: "Event processed concurrently" };
      }

      return {
        success: false,
        message: `Webhook processing failed: ${String(error)}`,
      };
    }
  },
});

async function handleCheckoutSessionCompleted(
  ctx: any,
  session: Stripe.Checkout.Session
) {
  console.log("Checkout session completed:", session.id);

  if (session.mode === "subscription" && session.subscription) {
    // Get the subscription details
    const stripe = getStripe();
    const subscription = await stripe.subscriptions.retrieve(
      session.subscription as string
    );
    await handleSubscriptionUpdated(ctx, subscription);
  }
}

async function handleSubscriptionUpdated(
  ctx: any,
  subscription: Stripe.Subscription
) {
  console.log("Subscription updated:", subscription.id);

  const customerId = subscription.customer as string;
  const plan = subscription.metadata.plan as "pro" | "ultra" | "max";

  if (!plan || !["pro", "ultra", "max"].includes(plan)) {
    console.error("Invalid plan in subscription metadata:", plan);
    return;
  }

  // Get customer details from Stripe
  const stripe = getStripe();
  const customer = await stripe.customers.retrieve(customerId);

  // Validate and ensure we have valid period dates
  let currentPeriodStart = (subscription as any).current_period_start;
  let currentPeriodEnd = (subscription as any).current_period_end;

  // Check if period dates are invalid (null, undefined, or 0)
  if (!currentPeriodStart || currentPeriodStart === 0) {
    console.warn(
      `Invalid current_period_start (${currentPeriodStart}) for subscription ${subscription.id}, calculating fallback`
    );
    // Use subscription.created as fallback, or current time if that's also invalid
    const fallbackStart = subscription.created || Math.floor(Date.now() / 1000);
    currentPeriodStart = fallbackStart;
  }

  if (!currentPeriodEnd || currentPeriodEnd === 0) {
    console.warn(
      `Invalid current_period_end (${currentPeriodEnd}) for subscription ${subscription.id}, calculating fallback`
    );
    // Calculate 30 days from the start date
    const fallbackEnd = currentPeriodStart + 30 * 24 * 60 * 60; // 30 days in seconds
    currentPeriodEnd = fallbackEnd;
  }

  // Ensure we have valid numbers before multiplying by 1000
  const validatedPeriodStart = Number(currentPeriodStart) * 1000;
  const validatedPeriodEnd = Number(currentPeriodEnd) * 1000;

  // Check if this is a new subscription (active status and not updating an existing one)
  const isNewSubscription =
    subscription.status === "active" || subscription.status === "trialing";

  // Double-check that our calculations resulted in valid timestamps
  if (isNaN(validatedPeriodStart) || isNaN(validatedPeriodEnd)) {
    console.error(
      `Failed to calculate valid period dates for subscription ${subscription.id}`
    );
    // Use current time + 30 days as absolute fallback
    const now = Date.now();
    const fallbackStart = now;
    const fallbackEnd = now + 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

    await ctx.runMutation(internal.stripeHelpers.updateSubscription, {
      stripeCustomerId: customerId,
      stripeSubscriptionId: subscription.id,
      plan,
      status: subscription.status,
      currentPeriodStart: fallbackStart,
      currentPeriodEnd: fallbackEnd,
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
    });
  } else {
    await ctx.runMutation(internal.stripeHelpers.updateSubscription, {
      stripeCustomerId: customerId,
      stripeSubscriptionId: subscription.id,
      plan,
      status: subscription.status,
      currentPeriodStart: validatedPeriodStart,
      currentPeriodEnd: validatedPeriodEnd,
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
    });
  }

  // Send subscription confirmation email for new active subscriptions
  if (isNewSubscription && customer && !customer.deleted) {
    try {
      const customerEmail = customer.email;
      const customerName = customer.name;

      if (customerEmail) {
        // Get plan pricing information
        const planPrices = {
          pro: "$19.99",
          ultra: "$49.99",
          max: "$99.99",
        };

        // Format the subscription end date
        const endDate = new Date(validatedPeriodEnd).toLocaleDateString(
          "en-US",
          {
            year: "numeric",
            month: "long",
            day: "numeric",
          }
        );

        await ctx.runAction(internal.email.sendSubscriptionEmail, {
          email: customerEmail,
          userName: customerName || undefined,
          planName: plan,
          planPrice: planPrices[plan],
          currentPeriodEnd: endDate,
          dashboardUrl: "https://ai.erzen.tk/settings",
        });

        console.log(
          `Subscription confirmation email sent to ${customerEmail} for plan ${plan}`
        );
      }
    } catch (error) {
      console.error("Failed to send subscription confirmation email:", error);
      // Don't throw - subscription should still be processed even if email fails
    }
  }
}

async function handleSubscriptionDeleted(
  ctx: any,
  subscription: Stripe.Subscription
) {
  console.log("Subscription deleted:", subscription.id);

  await ctx.runMutation(internal.stripeHelpers.cancelSubscriptionInDb, {
    stripeSubscriptionId: subscription.id,
  });
}

async function handleInvoicePaymentSucceeded(
  ctx: any,
  invoice: Stripe.Invoice
) {
  console.log("Invoice payment succeeded:", invoice.id);

  if ((invoice as any).subscription) {
    // Refresh subscription data when payment succeeds
    const stripe = getStripe();
    const subscription = await stripe.subscriptions.retrieve(
      (invoice as any).subscription as string
    );
    await handleSubscriptionUpdated(ctx, subscription);
  }
}

async function handleInvoicePaymentFailed(ctx: any, invoice: Stripe.Invoice) {
  console.log("Invoice payment failed:", invoice.id);

  if ((invoice as any).subscription) {
    // You might want to handle failed payments here
    // For example, send an email notification or update subscription status
    const stripe = getStripe();
    const subscription = await stripe.subscriptions.retrieve(
      (invoice as any).subscription as string
    );
    await handleSubscriptionUpdated(ctx, subscription);
  }
}
