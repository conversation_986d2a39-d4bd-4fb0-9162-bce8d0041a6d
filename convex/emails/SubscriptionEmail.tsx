import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  <PERSON>tm<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Section,
  Text,
} from "@react-email/components";
import React from "react";

interface SubscriptionEmailProps {
  userEmail: string;
  userName?: string;
  planName: "pro" | "ultra" | "max";
  planPrice: string;
  currentPeriodEnd: string;
  dashboardUrl?: string;
}

const getPlanEmoji = (plan: string) => {
  switch (plan) {
    case "pro": return "⭐";
    case "ultra": return "🚀";
    case "max": return "💎";
    default: return "✨";
  }
};

const getPlanFeatures = (plan: string) => {
  switch (plan) {
    case "pro":
      return [
        "100,000 AI credits per month",
        "Access to premium AI models",
        "Priority support",
        "Advanced conversation features"
      ];
    case "ultra":
      return [
        "500,000 AI credits per month",
        "Access to all AI models",
        "Priority support",
        "Advanced conversation features",
        "API access",
        "Custom integrations"
      ];
    case "max":
      return [
        "Unlimited AI credits",
        "Access to all AI models",
        "Premium support",
        "Advanced conversation features",
        "API access",
        "Custom integrations",
        "White-label options"
      ];
    default:
      return ["Access to premium features"];
  }
};

export const SubscriptionEmail = ({
  userEmail = "<EMAIL>",
  userName = "there",
  planName = "pro",
  planPrice = "$19.99",
  currentPeriodEnd = "January 1, 2025",
  dashboardUrl = "https://app.example.com/settings",
}: SubscriptionEmailProps) => {
  const planEmoji = getPlanEmoji(planName);
  const features = getPlanFeatures(planName);

  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Text style={headerText}>
              {planEmoji} Welcome to {planName.charAt(0).toUpperCase() + planName.slice(1)}!
            </Text>
          </Section>
          
          <Section style={content}>
            <Text style={greeting}>Hi {userName}! 🎉</Text>
            
            <Text style={paragraph}>
              Thank you for subscribing to Erzen AI {planName.charAt(0).toUpperCase() + planName.slice(1)}! 
              Your subscription is now active and you have access to all the premium features.
            </Text>
            
            <Section style={subscriptionDetails}>
              <Text style={detailsTitle}>Subscription Details</Text>
              <Text style={detailItem}><strong>Plan:</strong> {planName.charAt(0).toUpperCase() + planName.slice(1)}</Text>
              <Text style={detailItem}><strong>Price:</strong> {planPrice}/month</Text>
              <Text style={detailItem}><strong>Next billing date:</strong> {currentPeriodEnd}</Text>
              <Text style={detailItem}><strong>Email:</strong> {userEmail}</Text>
            </Section>
            
            <Section style={buttonContainer}>
              <Button pX={30} pY={15} style={button} href={dashboardUrl}>
                Access Your Dashboard
              </Button>
            </Section>
            
            <Section style={featuresBox}>
              <Text style={featuresTitle}>🚀 Your {planName.charAt(0).toUpperCase() + planName.slice(1)} features:</Text>
              <ul style={featuresList}>
                {features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </Section>

            <Section style={tipsBox}>
              <Text style={tipsTitle}>💡 Getting Started Tips:</Text>
              <ul style={featuresList}>
                <li>Explore the new AI models available in your model selector</li>
                <li>Try uploading documents for AI analysis</li>
                <li>Use the advanced conversation features</li>
                <li>Check out the usage dashboard to monitor your credits</li>
              </ul>
            </Section>
            
            <Hr style={hr} />
            
            <Text style={support}>
              Need help getting started? We're here to help! 
              <Link href="mailto:<EMAIL>" style={link}>
                Contact our support team
              </Link> or visit our 
              <Link href={dashboardUrl} style={link}> dashboard</Link> to manage your subscription.
            </Text>
            
            <Text style={disclaimer}>
              You can manage your subscription, view invoices, and update your billing information 
              in your dashboard at any time.
            </Text>
          </Section>
          
          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Erzen AI. Made with ❤️ for better conversations.
            </Text>
            <Text style={footerText}>
              <Link href={dashboardUrl} style={footerLink}>Manage Subscription</Link> • 
              <Link href="mailto:<EMAIL>" style={footerLink}> Support</Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "0",
  marginTop: "30px",
  marginBottom: "30px",
  borderRadius: "8px",
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
  maxWidth: "600px",
};

const header = {
  background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
  padding: "30px",
  textAlign: "center" as const,
  borderRadius: "8px 8px 0 0",
};

const headerText = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  margin: "0",
};

const content = {
  padding: "30px",
};

const greeting = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#495057",
  marginTop: "0",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.6",
  color: "#374151",
  marginBottom: "20px",
};

const subscriptionDetails = {
  backgroundColor: "#f8fafc",
  padding: "20px",
  borderRadius: "8px",
  border: "1px solid #e2e8f0",
  margin: "20px 0",
};

const detailsTitle = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#1e293b",
  marginTop: "0",
  marginBottom: "15px",
};

const detailItem = {
  fontSize: "14px",
  color: "#475569",
  margin: "8px 0",
  lineHeight: "1.5",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "30px 0",
};

const button = {
  background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
  borderRadius: "25px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "inline-block",
  boxShadow: "0 4px 15px rgba(59, 130, 246, 0.3)",
};

const featuresBox = {
  backgroundColor: "#ecfdf5",
  padding: "20px",
  borderRadius: "8px",
  margin: "20px 0",
  border: "1px solid #d1fae5",
};

const featuresTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#065f46",
};

const featuresList = {
  margin: "10px 0 0 0",
  paddingLeft: "20px",
  color: "#374151",
};

const tipsBox = {
  backgroundColor: "#fefce8",
  padding: "20px",
  borderRadius: "8px",
  margin: "20px 0",
  border: "1px solid #fde68a",
};

const tipsTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#92400e",
};

const hr = {
  borderColor: "#e5e7eb",
  margin: "30px 0",
};

const support = {
  fontSize: "14px",
  color: "#374151",
  lineHeight: "1.6",
  marginBottom: "15px",
};

const disclaimer = {
  fontSize: "14px",
  color: "#6b7280",
  lineHeight: "1.5",
  marginBottom: "10px",
};

const link = {
  color: "#3b82f6",
  textDecoration: "underline",
};

const footer = {
  textAlign: "center" as const,
  marginTop: "20px",
  padding: "20px",
  backgroundColor: "#f9fafb",
  borderRadius: "0 0 8px 8px",
};

const footerText = {
  fontSize: "12px",
  color: "#6b7280",
  margin: "5px 0",
};

const footerLink = {
  color: "#6b7280",
  textDecoration: "underline",
};

export default SubscriptionEmail;