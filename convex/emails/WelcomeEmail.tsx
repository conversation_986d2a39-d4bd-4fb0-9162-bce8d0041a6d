import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  <PERSON>tml,
  <PERSON><PERSON>,
  <PERSON>,
  Section,
  Text,
} from "@react-email/components";
import React from "react";

interface WelcomeEmailProps {
  userEmail: string;
  verificationUrl: string;
}

export const WelcomeEmail = ({
  userEmail = "<EMAIL>",
  verificationUrl = "https://app.example.com/verify",
}: WelcomeEmailProps) => {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Text style={headerText}>Welcome to Erzen AI! 🎉</Text>
          </Section>
          
          <Section style={content}>
            <Text style={greeting}>Hi there! 👋</Text>
            
            <Text style={paragraph}>
              Welcome to Erzen AI, your advanced AI-powered chatbot application! 
              We're excited to have you on board. To get started and secure your account, 
              please verify your email address.
            </Text>
            
            <Section style={callout}>
              <Text style={calloutText}>
                ✅ Click the button below to verify your email and activate your account:
              </Text>
            </Section>
            
            <Section style={buttonContainer}>
              <Button pX={30} pY={15} style={button} href={verificationUrl}>
                Verify Email Address
              </Button>
            </Section>
            
            <Section style={featuresBox}>
              <Text style={featuresTitle}>🚀 What you can do once verified:</Text>
              <ul style={featuresList}>
                <li>Chat with our advanced AI assistant</li>
                <li>Create and manage multiple conversations</li>
                <li>Upload files for AI analysis</li>
                <li>Customize your chat experience</li>
                <li>Access premium AI models</li>
              </ul>
            </Section>
            
            <Hr style={hr} />
            
            <Text style={disclaimer}>
              If you didn't create an account with us, you can safely ignore this email.
            </Text>
            
            <Text style={disclaimer}>
              If the button doesn't work, copy and paste this link into your browser:
              <br />
              <Link href={verificationUrl} style={link}>
                {verificationUrl}
              </Link>
            </Text>
          </Section>
          
          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Erzen AI. Made with ❤️ for better conversations.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "0",
  marginTop: "30px",
  marginBottom: "30px",
  borderRadius: "8px",
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
  maxWidth: "600px",
};

const header = {
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  padding: "30px",
  textAlign: "center" as const,
  borderRadius: "8px 8px 0 0",
};

const headerText = {
  color: "#ffffff",
  fontSize: "28px",
  fontWeight: "bold",
  margin: "0",
};

const content = {
  padding: "30px",
};

const greeting = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#495057",
  marginTop: "0",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.6",
  color: "#374151",
  marginBottom: "20px",
};

const callout = {
  backgroundColor: "#ffffff",
  padding: "20px",
  borderRadius: "8px",
  border: "1px solid #e5e7eb",
  borderLeft: "4px solid #10b981",
  margin: "20px 0",
};

const calloutText = {
  margin: "0",
  fontWeight: "600",
  color: "#10b981",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "30px 0",
};

const button = {
  background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
  borderRadius: "25px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "inline-block",
  boxShadow: "0 4px 15px rgba(16, 185, 129, 0.3)",
};

const featuresBox = {
  backgroundColor: "#eff6ff",
  padding: "20px",
  borderRadius: "8px",
  margin: "20px 0",
};

const featuresTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#1d4ed8",
};

const featuresList = {
  margin: "10px 0 0 0",
  paddingLeft: "20px",
  color: "#374151",
};

const hr = {
  borderColor: "#e5e7eb",
  margin: "30px 0",
};

const disclaimer = {
  fontSize: "14px",
  color: "#6b7280",
  lineHeight: "1.5",
  marginBottom: "10px",
};

const link = {
  color: "#3b82f6",
  textDecoration: "underline",
  wordBreak: "break-all" as const,
};

const footer = {
  textAlign: "center" as const,
  marginTop: "20px",
  padding: "20px",
  backgroundColor: "#f9fafb",
  borderRadius: "0 0 8px 8px",
};

const footerText = {
  fontSize: "12px",
  color: "#6b7280",
  margin: "0",
};

export default WelcomeEmail;