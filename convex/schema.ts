import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  conversations: defineTable({
    userId: v.id("users"),
    title: v.string(),
    lastMessageAt: v.number(),
    currentBranch: v.optional(v.string()),
    isGenerationCancelled: v.optional(v.boolean()),
    isGenerating: v.optional(v.boolean()),
    generatingMessageId: v.optional(v.id("messages")),
    isPinned: v.optional(v.boolean()),
    shareId: v.optional(v.string()),
    isShared: v.optional(v.boolean()),
    sharedAt: v.optional(v.number()),
    exportedAt: v.optional(v.number()),
    messageCount: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_share_id", ["shareId"])
    .index("by_user_pinned", ["userId", "isPinned"]),

  conversationBranches: defineTable({
    conversationId: v.id("conversations"),
    branchId: v.string(),
    parentBranchId: v.optional(v.string()),
    branchPoint: v.optional(v.id("messages")),
    title: v.string(),
    createdAt: v.number(),
    isActive: v.boolean(),
    messageCount: v.optional(v.number()),
    lastMessageAt: v.optional(v.number()),
  })
    .index("by_conversation", ["conversationId"])
    .index("by_conversation_branch", ["conversationId", "branchId"]),

  messages: defineTable({
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
    parentMessageId: v.optional(v.id("messages")),
    role: v.union(
      v.literal("user"),
      v.literal("assistant"),
      v.literal("system"),
      v.literal("tool")
    ),
    content: v.string(),
    thinking: v.optional(v.string()),
    attachments: v.optional(
      v.array(
        v.object({
          type: v.union(
            v.literal("image"),
            v.literal("file"),
            v.literal("audio"),
            v.literal("video")
          ),
          url: v.string(),
          name: v.string(),
          size: v.optional(v.number()),
          storageId: v.optional(v.id("_storage")),
          extractedText: v.optional(v.string()),
          mimeType: v.optional(v.string()),
        })
      )
    ),
    toolCalls: v.optional(
      v.array(
        v.object({
          id: v.string(),
          name: v.string(),
          arguments: v.string(),
          result: v.optional(v.string()),
          startTime: v.optional(v.number()),
          endTime: v.optional(v.number()),
        })
      )
    ),
    toolCallId: v.optional(v.string()),
    contentSequence: v.optional(
      v.array(
        v.union(
          v.object({
            type: v.literal("content"),
            text: v.string(),
            timestamp: v.number(),
          }),
          v.object({
            type: v.literal("tool"),
            toolCallId: v.string(),
            timestamp: v.number(),
          })
        )
      )
    ),
    generationMetrics: v.optional(
      v.object({
        provider: v.string(),
        model: v.string(),
        tokensUsed: v.optional(v.number()),
        inputTokens: v.optional(v.number()),
        outputTokens: v.optional(v.number()),
        completionTokens: v.optional(v.number()), // Legacy field from pre-v5 migration
        promptTokens: v.optional(v.number()), // Legacy field from pre-v5 migration
        generationTimeMs: v.optional(v.number()),
        tokensPerSecond: v.optional(v.number()),
        finishReason: v.optional(v.string()),
        temperature: v.optional(v.number()),
        maxOutputTokens: v.optional(v.number()),
        timeToFirstTokenMs: v.optional(v.number()),
        timeToFirstContentMs: v.optional(v.number()),
        timeToFirstToolMs: v.optional(v.number()),
        reasoningTimeMs: v.optional(v.number()),
        toolExecutionTimeMs: v.optional(v.number()),
        attemptedProviders: v.optional(v.array(v.string())),
        fallbackUsed: v.optional(v.boolean()),
      })
    ),
    isEdited: v.optional(v.boolean()),
    editedAt: v.optional(v.number()),
    isError: v.optional(v.boolean()),
    canvasData: v.optional(
      v.union(
        // Single canvas object (legacy / simple)
        v.object({
          type: v.union(
            v.literal("markdown"),
            v.literal("code"),
            v.literal("chart"),
            v.literal("react")
          ),
          title: v.string(),
          content: v.string(),
          language: v.optional(v.string()),
          chartSpec: v.optional(v.string()),
          library: v.optional(
            v.union(v.literal("chartjs"), v.literal("echarts"), v.literal("d3"))
          ),
          updatedAt: v.number(),
        }),
        // Support multiple canvases per message
        v.array(
          v.object({
            type: v.union(
              v.literal("markdown"),
              v.literal("code"),
              v.literal("chart"),
              v.literal("react")
            ),
            title: v.string(),
            content: v.string(),
            language: v.optional(v.string()),
            chartSpec: v.optional(v.string()),
            library: v.optional(
              v.union(
                v.literal("chartjs"),
                v.literal("echarts"),
                v.literal("d3")
              )
            ),
            updatedAt: v.number(),
          })
        )
      )
    ),
  })
    .index("by_conversation", ["conversationId"])
    .index("by_conversation_branch", ["conversationId", "branchId"])
    .index("by_parent", ["parentMessageId"]),

  uploadedFiles: defineTable({
    userId: v.id("users"),
    storageId: v.id("_storage"),
    fileName: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    extractedText: v.optional(v.string()),
    uploadedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_storage_id", ["storageId"]),

  generatedImages: defineTable({
    userId: v.id("users"),
    storageId: v.id("_storage"),
    prompt: v.string(),
    style: v.optional(v.string()),
    aspectRatio: v.optional(v.string()),
    model: v.string(),
    generatedAt: v.number(),
  }).index("by_user", ["userId"]),

  userPreferences: defineTable({
    userId: v.id("users"),
    aiProvider: v.union(
      v.literal("openai"),
      v.literal("anthropic"),
      v.literal("google"),
      v.literal("openrouter"),
      v.literal("groq"),
      v.literal("deepseek"),
      v.literal("grok"),
      v.literal("cohere"),
      v.literal("mistral"),
      v.literal("cerebras"),
      v.literal("github"),
      v.literal("system")
    ),
    model: v.string(),
    temperature: v.number(),
    maxOutputTokens: v.optional(v.number()),
    maxTokens: v.optional(v.number()), // Legacy field from pre-v5 migration
    enabledTools: v.optional(v.array(v.string())),
    previousEnabledTools: v.optional(v.array(v.string())),
    favoriteModels: v.optional(
      v.array(
        v.object({
          provider: v.string(),
          model: v.string(),
        })
      )
    ),
    modelReasoningEfforts: v.optional(
      v.record(v.string(), v.union(v.string(), v.number()))
    ),
    reasoningEffort: v.optional(v.union(v.string(), v.number())),
    hideUserInfo: v.optional(v.boolean()),
    showToolOutputs: v.optional(v.boolean()),
    showMessageMetadata: v.optional(v.boolean()),
    systemPrompt: v.optional(v.string()),
    useCustomSystemPrompt: v.optional(v.boolean()),
    showThinking: v.optional(v.boolean()),
    imageModel: v.optional(v.string()),
    theme: v.optional(
      v.union(v.literal("light"), v.literal("dark"), v.literal("system"))
    ),
    colorTheme: v.optional(v.string()),
    uiTheme: v.optional(v.string()),
    enterToSend: v.optional(v.boolean()),
  }).index("by_user", ["userId"]),

  apiKeys: defineTable({
    userId: v.id("users"),
    provider: v.union(
      v.literal("openai"),
      v.literal("anthropic"),
      v.literal("google"),
      v.literal("openrouter"),
      v.literal("groq"),
      v.literal("deepseek"),
      v.literal("grok"),
      v.literal("cohere"),
      v.literal("mistral"),
      v.literal("tavily"),
      v.literal("exa"),
      v.literal("instant"),
      v.literal("openweather"),
      v.literal("firecrawl"),
      v.literal("cerebras"),
      v.literal("cloudflare"),
      v.literal("github")
    ),
    apiKey: v.string(),
    isActive: v.boolean(),
  }).index("by_user_provider", ["userId", "provider"]),

  customProviders: defineTable({
    userId: v.id("users"),
    name: v.string(),
    displayName: v.string(),
    baseURL: v.string(),
    apiKey: v.string(),
    models: v.array(v.string()),
    icon: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_name", ["userId", "name"]),

  userUsage: defineTable({
    userId: v.id("users"),
    plan: v.union(
      v.literal("free"),
      v.literal("pro"),
      v.literal("ultra"),
      v.literal("max")
    ),
    creditsUsed: v.number(),
    creditsLimit: v.number(),
    maxSpendingDollars: v.number(),
    dollarsSpent: v.number(),
    searchesUsed: v.number(),
    resetDate: v.number(),
    // Stripe subscription fields
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    subscriptionStatus: v.optional(
      v.union(
        v.literal("active"),
        v.literal("canceled"),
        v.literal("incomplete"),
        v.literal("incomplete_expired"),
        v.literal("past_due"),
        v.literal("trialing"),
        v.literal("unpaid")
      )
    ),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
  })
    .index("by_user", ["userId"])
    .index("by_stripe_customer", ["stripeCustomerId"])
    .index("by_stripe_subscription", ["stripeSubscriptionId"]),

  usageEvents: defineTable({
    userId: v.id("users"),
    type: v.union(v.literal("chat"), v.literal("image"), v.literal("search")),
    model: v.optional(v.string()),
    provider: v.optional(v.string()),
    credits: v.number(),
    dollars: v.number(),
    inputTokens: v.optional(v.number()),
    outputTokens: v.optional(v.number()),
    createdAt: v.number(),
    conversationId: v.optional(v.id("conversations")),
    messageId: v.optional(v.id("messages")),
  })
    .index("by_user", ["userId"])
    .index("by_user_date", ["userId", "createdAt"])
    .index("by_user_type", ["userId", "type"]),

  stripeWebhookEvents: defineTable({
    stripeEventId: v.string(),
    eventType: v.string(),
    processed: v.boolean(),
    processedAt: v.optional(v.number()),
  })
    .index("by_stripe_event_id", ["stripeEventId"])
    .index("by_processed", ["processed"]),

  userInstructions: defineTable({
    userId: v.id("users"),
    instructions: v.string(),
  }).index("by_user", ["userId"]),

  userMemories: defineTable({
    userId: v.id("users"),
    memory: v.string(),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  mcpServers: defineTable({
    userId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    transportType: v.union(
      v.literal("stdio"),
      v.literal("sse"),
      v.literal("http"),
      v.literal("browser")
    ),
    // For stdio transport
    command: v.optional(v.string()),
    args: v.optional(v.array(v.string())),
    // For SSE/HTTP transport
    url: v.optional(v.string()),
    headers: v.optional(v.record(v.string(), v.string())),
    // For browser transport
    runtime: v.optional(
      v.union(v.literal("node"), v.literal("python"), v.literal("wasm"))
    ),
    sourceCode: v.optional(v.string()),
    packageDependencies: v.optional(v.record(v.string(), v.string())),
    // Common fields
    isEnabled: v.boolean(),
    createdAt: v.number(),
    lastUsed: v.optional(v.number()),
    // Cached tool info
    availableTools: v.optional(v.array(v.string())),
    toolsLastUpdated: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_user_enabled", ["userId", "isEnabled"]),

  n8nWorkflows: defineTable({
    userId: v.string(),
    name: v.string(),
    description: v.optional(v.string()),
    triggerType: v.union(v.literal("webhook"), v.literal("api")),

    // For webhook trigger type
    webhookUrl: v.optional(v.string()),

    // For API trigger type
    apiUrl: v.optional(v.string()),
    apiKey: v.optional(v.string()),
    workflowId: v.optional(v.string()),

    // Shared fields
    parametersSchema: v.optional(v.string()), // JSON schema for input parameters
    isEnabled: v.boolean(),
    createdAt: v.number(),
    lastUsed: v.optional(v.number()),
    successCount: v.optional(v.number()),
    failureCount: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_user_enabled", ["userId", "isEnabled"]),

  n8nServers: defineTable({
    userId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    apiUrl: v.string(),
    apiKey: v.optional(v.string()),
    isEnabled: v.boolean(),
    createdAt: v.number(),
    lastUsed: v.optional(v.number()),
    availableTools: v.optional(v.array(v.string())),
    toolsLastUpdated: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_user_enabled", ["userId", "isEnabled"]),

  // Stores GitHub OAuth credentials linked to a user
  githubAccounts: defineTable({
    userId: v.id("users"),
    githubId: v.string(), // Numeric GitHub user id as string
    username: v.string(),
    accessToken: v.string(),
    linkedAt: v.number(),
  }).index("by_user", ["userId"]),

  // Cached repository metadata and files
  repoBranches: defineTable({
    // Optional user association (null for public cache)
    userId: v.optional(v.id("users")),
    repoFullName: v.string(), // e.g. owner/repo
    branch: v.string(),
    commitSha: v.string(),
    fetchedAt: v.number(),
  }).index("by_repo_branch", ["repoFullName", "branch"]),

  repoFiles: defineTable({
    repoFullName: v.string(),
    branch: v.string(),
    path: v.string(),
    sha: v.string(),
    size: v.number(),
    // Store up to ~100 KB inline; larger/binary files can omit content
    content: v.optional(v.string()),
    fetchedAt: v.number(),
  })
    .index("by_repo_branch", ["repoFullName", "branch"])
    .index("by_repo_branch_path", ["repoFullName", "branch", "path"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
