import { convexAuth, getAuthUserId } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { Email } from "@convex-dev/auth/providers/Email";
import GitHub from "@auth/core/providers/github";
import Google from "@auth/core/providers/google";
import { query } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Password,
    Email({
      sendVerificationRequest: async ({ identifier: email, url }) => {
        try {
          // For now, just log the verification URL
          // In production, you would integrate with your email service here
          console.log("Verification email would be sent to:", email);
          console.log("Verification URL:", url);

          // TODO: Implement email sending when user registration is completed
          // This would be called from the user registration flow instead
        } catch (error) {
          console.error("Failed to send verification email:", error);
          // In development, log the verification URL as fallback
          console.log("Verification URL (fallback):", url);
        }
      },
    }),
    GitHub,
    Google,
  ],
});

export const loggedInUser = query({
  args: {},
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id("users"),
      _creationTime: v.number(),
      name: v.optional(v.string()),
      email: v.optional(v.string()),
      image: v.optional(v.string()),
      emailVerificationTime: v.optional(v.number()),
      isAnonymous: v.optional(v.boolean()),
    })
  ),
  handler: async (ctx: any) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    // Use direct get with ID instead of potentially scanning tables
    const user = await ctx.db.get(userId);
    if (!user) {
      return null;
    }

    // Return only the needed fields to reduce data transfer
    return {
      _id: user._id,
      _creationTime: user._creationTime,
      name: user.name,
      email: user.email,
      image: user.image,
      emailVerificationTime: user.emailVerificationTime,
      isAnonymous: user.isAnonymous,
    };
  },
});

/**
 * Check if the current user has OAuth authentication (GitHub or Google)
 * Returns true if user signed in with OAuth, false if they used password/email
 */
export const hasOAuthAuth = query({
  args: {},
  returns: v.boolean(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return false;
    }

    // Check for GitHub account first (more specific query)
    const githubAccount = await ctx.db
      .query("authAccounts")
      .withIndex("userIdAndProvider", (q) =>
        q.eq("userId", userId).eq("provider", "github")
      )
      .first();

    if (githubAccount) {
      return true;
    }

    // Check for Google account
    const googleAccount = await ctx.db
      .query("authAccounts")
      .withIndex("userIdAndProvider", (q) =>
        q.eq("userId", userId).eq("provider", "google")
      )
      .first();

    return !!googleAccount;
  },
});
