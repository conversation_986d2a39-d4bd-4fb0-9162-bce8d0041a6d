"use node";

import { action, internalAction, internalMutation } from "./_generated/server";
import { components } from "./_generated/api";
import { v } from "convex/values";
import { Resend } from "@convex-dev/resend";
import { render } from "@react-email/render";
import WelcomeEmail from "./emails/WelcomeEmail";
import SubscriptionEmail from "./emails/SubscriptionEmail";



// Initialize Resend component
export const resend: Resend = new Resend(components.resend, {
  testMode: false, // Set to false for production
});

// Send verification email using React Email template
export const sendVerificationEmail = internalAction({
  args: {
    email: v.string(),
    verificationUrl: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      const emailHtml = await render(
        WelcomeEmail({
          userEmail: args.email,
          verificationUrl: args.verificationUrl,
        })
      );

      await resend.sendEmail(ctx, {
        from: "Erzen AI <<EMAIL>>",
        to: args.email,
        subject: "Welcome to Erzen AI! Please verify your email",
        html: emailHtml,
      });

      console.log(`Verification email sent to ${args.email}`);
    } catch (error) {
      console.error("Failed to send verification email:", error);
      throw new Error("Failed to send verification email");
    }

    return null;
  },
});

// Send subscription confirmation email
export const sendSubscriptionEmail = internalAction({
  args: {
    email: v.string(),
    userName: v.optional(v.string()),
    planName: v.union(v.literal("pro"), v.literal("ultra"), v.literal("max")),
    planPrice: v.string(),
    currentPeriodEnd: v.string(),
    dashboardUrl: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      const emailHtml = await render(
        SubscriptionEmail({
          userEmail: args.email,
          userName: args.userName,
          planName: args.planName,
          planPrice: args.planPrice,
          currentPeriodEnd: args.currentPeriodEnd,
          dashboardUrl:
            args.dashboardUrl || "https://app.erzen-ai.com/settings",
        })
      );

      await resend.sendEmail(ctx, {
        from: "Erzen AI <<EMAIL>>",
        to: args.email,
        subject: `Welcome to ${args.planName.charAt(0).toUpperCase() + args.planName.slice(1)}! Your subscription is active`,
        html: emailHtml,
      });

      console.log(
        `Subscription email sent to ${args.email} for plan ${args.planName}`
      );
    } catch (error) {
      console.error("Failed to send subscription email:", error);
      throw new Error("Failed to send subscription email");
    }

    return null;
  },
});
