import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";
import { paginationOptsValidator } from "convex/server";
import { Id } from "./_generated/dataModel";

export const list = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();

    // Sort to show pinned conversations first, then by lastMessageAt
    return conversations.sort((a, b) => {
      // First sort by pinned status (pinned first)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then sort by lastMessageAt (newest first)
      return b.lastMessageAt - a.lastMessageAt;
    });
  },
});

export const get = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    return conversation;
  },
});

export const create = mutation({
  args: { title: v.string() },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Use a single transaction to create both conversation and main branch
    const conversationId = await ctx.db.insert("conversations", {
      userId,
      title: args.title,
      lastMessageAt: Date.now(),
      currentBranch: "main",
      messageCount: 0,
    });

    // Create the main branch in the same transaction
    await ctx.db.insert("conversationBranches", {
      conversationId,
      branchId: "main",
      title: "Main",
      createdAt: Date.now(),
      isActive: true,
      messageCount: 0,
      lastMessageAt: Date.now(),
    });

    return conversationId;
  },
});

// Optimized mutation to create conversation and add first message in single operation
export const createWithFirstMessage = mutation({
  args: {
    title: v.string(),
    content: v.string(),
    attachments: v.optional(
      v.array(
        v.object({
          type: v.union(
            v.literal("image"),
            v.literal("file"),
            v.literal("audio"),
            v.literal("video")
          ),
          url: v.string(),
          name: v.string(),
          size: v.optional(v.number()),
          storageId: v.optional(v.id("_storage")),
          extractedText: v.optional(v.string()),
          mimeType: v.optional(v.string()),
        })
      )
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const now = Date.now();

    // Create conversation and main branch in a single transaction
    const conversationId = await ctx.db.insert("conversations", {
      userId,
      title: args.title,
      lastMessageAt: now,
      currentBranch: "main",
      messageCount: 1,
    });

    await ctx.db.insert("conversationBranches", {
      conversationId,
      branchId: "main",
      title: "Main",
      createdAt: now,
      isActive: true,
      messageCount: 1,
      lastMessageAt: now,
    });

    // Add the first user message immediately
    const messageId = await ctx.db.insert("messages", {
      conversationId,
      branchId: "main",
      role: "user",
      content: args.content,
      attachments: args.attachments,
    });

    return { conversationId, messageId };
  },
});

export const updateTitle = mutation({
  args: {
    conversationId: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      title: args.title,
    });
  },
});

export const remove = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Get all messages in the conversation to find storage files to delete
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .collect();

    // Collect all storage IDs that need to be deleted
    const storageIdsToDelete: Array<string> = [];

    for (const message of messages) {
      if (message.attachments) {
        for (const attachment of message.attachments) {
          if (attachment.storageId) {
            storageIdsToDelete.push(attachment.storageId);
          }
        }
      }
    }

    // Delete all storage files
    for (const storageId of storageIdsToDelete) {
      try {
        await ctx.storage.delete(storageId as any);
      } catch (error) {
        // Log error but don't fail the deletion - file might already be gone
        console.warn(`Failed to delete storage file ${storageId}:`, error);
      }
    }

    // Delete all file metadata records associated with these storage IDs
    for (const storageId of storageIdsToDelete) {
      try {
        const fileRecord = await ctx.db
          .query("uploadedFiles")
          .withIndex("by_storage_id", (q) =>
            q.eq("storageId", storageId as any)
          )
          .unique();

        if (fileRecord) {
          await ctx.db.delete(fileRecord._id);
        }
      } catch (error) {
        console.warn(`Failed to delete file metadata for ${storageId}:`, error);
      }
    }

    // Delete all conversation branches
    const branches = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .collect();

    for (const branch of branches) {
      await ctx.db.delete(branch._id);
    }

    // Delete all messages in the conversation
    for (const message of messages) {
      await ctx.db.delete(message._id);
    }

    // Delete the conversation
    await ctx.db.delete(args.conversationId);
  },
});

export const duplicate = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const originalConversation = await ctx.db.get(args.conversationId);
    if (!originalConversation || originalConversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    const newConversationId = await ctx.db.insert("conversations", {
      userId,
      title: `Copy of ${originalConversation.title}`,
      lastMessageAt: originalConversation.lastMessageAt,
      currentBranch: "main",
    });

    // Create main branch for new conversation
    await ctx.db.insert("conversationBranches", {
      conversationId: newConversationId,
      branchId: "main",
      title: "Main",
      createdAt: Date.now(),
      isActive: true,
    });

    // Get the current active branch from original conversation
    const currentBranchId = originalConversation.currentBranch ?? "main";

    // Only copy messages from the current active branch
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q
          .eq("conversationId", args.conversationId)
          .eq("branchId", currentBranchId)
      )
      .order("asc")
      .collect();

    for (const message of messages) {
      await ctx.db.insert("messages", {
        conversationId: newConversationId,
        branchId: "main",
        role: message.role,
        content: message.content,
        thinking: message.thinking,
        attachments: message.attachments,
        toolCalls: message.toolCalls,
        toolCallId: message.toolCallId,
        generationMetrics: message.generationMetrics,
        canvasData: message.canvasData,
      });
    }

    return newConversationId;
  },
});

export const listWithMessageCounts = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const result = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .paginate(args.paginationOpts);

    const conversationsWithCounts = result.page.map((conversation) => ({
      ...conversation,
      messageCount: conversation.messageCount || 0,
    }));

    // Sort to show pinned conversations first, then by lastMessageAt
    const sortedConversations = conversationsWithCounts.sort((a, b) => {
      // First sort by pinned status (pinned first)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then sort by lastMessageAt (newest first)
      return b.lastMessageAt - a.lastMessageAt;
    });

    return {
      ...result,
      page: sortedConversations,
    };
  },
});

export const generateTitle = action({
  args: {
    conversationId: v.id("conversations"),
    firstUserMessage: v.optional(v.string()),
  },
  returns: v.string(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.runQuery(api.conversations.get, {
      conversationId: args.conversationId,
    });
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Build context for title generation.
    // If caller supplied a firstUserMessage we use it, otherwise we synthesise
    // a context string from up to the first message plus the most recent few messages.

    let contextText = "";

    if (args.firstUserMessage) {
      contextText = args.firstUserMessage;
    } else {
      // Fetch messages via query since actions cannot access the database directly.
      const messagesResult = await ctx.runQuery(api.messages.list, {
        conversationId: args.conversationId,
        paginationOpts: { numItems: 100, cursor: null },
      });

      const allMessages = messagesResult.page;

      if (allMessages.length > 0) {
        const firstMsg: string = allMessages[0].content;
        const recentMsgs: string[] = allMessages
          .slice(-5)
          .map((m: any) => m.content);
        contextText = [firstMsg, ...recentMsgs].join(" \n\n");
      }
    }

    // Ensure we don't send an excessively long prompt.
    const truncatedContext = contextText.split(" ").slice(0, 500).join(" ");

    // Generate title using AI
    try {
      const titleResponse: string = await ctx.runAction(api.ai.generateTitle, {
        userMessage: truncatedContext,
      });

      // Update the conversation title
      await ctx.runMutation(api.conversations.updateTitle, {
        conversationId: args.conversationId,
        title: titleResponse || "New Chat",
      });

      return titleResponse || "New Chat";
    } catch (error) {
      console.error("Failed to generate AI title:", error);

      // Fallback to simple title generation
      let fallbackTitle = truncatedContext
        .slice(0, 40)
        .split(" ")
        .slice(0, 6)
        .join(" ");

      if (truncatedContext.length > 40) {
        fallbackTitle += "...";
      }

      await ctx.runMutation(api.conversations.updateTitle, {
        conversationId: args.conversationId,
        title: fallbackTitle || "New Chat",
      });

      return fallbackTitle || "New Chat";
    }
  },
});

export const cancelGeneration = mutation({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isGenerationCancelled: true,
    });
  },
});

export const checkCancellation = query({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const conversation = await ctx.db.get(args.conversationId);
    return conversation?.isGenerationCancelled === true;
  },
});

export const clearCancellation = mutation({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isGenerationCancelled: false,
      isGenerating: false,
      generatingMessageId: undefined,
    });
  },
});

export const setGenerationState = mutation({
  args: {
    conversationId: v.id("conversations"),
    isGenerating: v.boolean(),
    messageId: v.optional(v.id("messages")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isGenerating: args.isGenerating,
      generatingMessageId: args.messageId,
    });
  },
});

export const checkGenerationState = query({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const conversation = await ctx.db.get(args.conversationId);
    return {
      isGenerating: conversation?.isGenerating === true,
      messageId: conversation?.generatingMessageId,
    };
  },
});

// ============ PINNING FUNCTIONALITY ============

export const togglePin = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isPinned: !conversation.isPinned,
    });

    return !conversation.isPinned;
  },
});

export const pinConversation = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isPinned: true,
    });
  },
});

export const unpinConversation = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      isPinned: false,
    });
  },
});

// ============ SHARING FUNCTIONALITY ============

export const shareConversation = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Generate a unique share ID
    const shareId = `share_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;

    await ctx.db.patch(args.conversationId, {
      shareId,
      isShared: true,
      sharedAt: Date.now(),
    });

    return shareId;
  },
});

export const unshareConversation = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      shareId: undefined,
      isShared: false,
      sharedAt: undefined,
    });
  },
});

export const getSharedConversation = query({
  args: { shareId: v.string() },
  returns: v.union(
    v.object({
      _id: v.id("conversations"),
      title: v.string(),
      lastMessageAt: v.number(),
      shareId: v.optional(v.string()),
      isShared: v.optional(v.boolean()),
      sharedAt: v.optional(v.number()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    // No authentication required for public shared conversations
    const conversation = await ctx.db
      .query("conversations")
      .withIndex("by_share_id", (q) => q.eq("shareId", args.shareId))
      .unique();

    if (!conversation || !conversation.isShared) {
      return null; // Return null if not found or not shared
    }

    // Return only safe public fields
    return {
      _id: conversation._id,
      title: conversation.title,
      lastMessageAt: conversation.lastMessageAt,
      shareId: conversation.shareId,
      isShared: conversation.isShared,
      sharedAt: conversation.sharedAt,
    };
  },
});

export const getSharedMessages = query({
  args: {
    shareId: v.string(),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.object({
    page: v.array(
      v.object({
        _id: v.id("messages"),
        _creationTime: v.number(),
        role: v.union(
          v.literal("user"),
          v.literal("assistant"),
          v.literal("system"),
          v.literal("tool")
        ),
        content: v.string(),
        thinking: v.optional(v.string()),
        attachments: v.optional(v.array(v.any())),
        toolCalls: v.optional(v.array(v.any())),
        generationMetrics: v.optional(v.any()),
      })
    ),
    isDone: v.boolean(),
    continueCursor: v.string(),
  }),
  handler: async (ctx, args) => {
    // No authentication required for public shared conversations
    const conversation = await ctx.db
      .query("conversations")
      .withIndex("by_share_id", (q) => q.eq("shareId", args.shareId))
      .unique();

    if (!conversation || !conversation.isShared) {
      throw new Error("Shared conversation not found");
    }

    const currentBranchId = conversation.currentBranch ?? "main";

    const result = await ctx.db
      .query("messages")
      .withIndex("by_conversation_branch", (q) =>
        q.eq("conversationId", conversation._id).eq("branchId", currentBranchId)
      )
      .order("asc")
      .paginate(args.paginationOpts);

    // Return only safe public fields from messages
    return {
      isDone: result.isDone,
      continueCursor: result.continueCursor,
      page: result.page.map((msg) => ({
        _id: msg._id,
        _creationTime: msg._creationTime,
        role: msg.role,
        content: msg.content,
        thinking: msg.thinking,
        attachments: msg.attachments,
        toolCalls: msg.toolCalls,
        generationMetrics: msg.generationMetrics,
        canvasData: msg.canvasData,
      })),
    };
  },
});

// ============ EXPORT/IMPORT FUNCTIONALITY ============

export const exportConversation = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Get all branches for this conversation
    const branches = await ctx.db
      .query("conversationBranches")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .collect();

    // Get all messages for this conversation
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .collect();

    return {
      version: "1.0",
      exportedAt: Date.now(),
      conversation: {
        title: conversation.title,
        lastMessageAt: conversation.lastMessageAt,
        currentBranch: conversation.currentBranch,
      },
      branches,
      messages: messages.map((msg) => ({
        ...msg,
        conversationId: undefined, // Remove to avoid ID conflicts on import
      })),
    };
  },
});

export const markExported = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      exportedAt: Date.now(),
    });
  },
});

export const importConversation = mutation({
  args: {
    exportData: v.any(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const exportData = args.exportData;

    // Basic validation
    if (!exportData || typeof exportData !== "object") {
      throw new Error("Invalid export data format");
    }

    // Handle different export formats
    if ("threads" in exportData) {
      // Handle external format with threads (like ChatGPT exports)
      let importedCount = 0;

      for (const thread of exportData.threads) {
        if (!thread.messages || thread.messages.length === 0) continue;

        const title =
          thread.title ?? thread.messages[0].content.substring(0, 50) + "...";
        const createdAt =
          typeof thread.createdAt === "string"
            ? new Date(thread.createdAt).getTime()
            : (thread.createdAt ?? Date.now());

        // Create conversation
        const conversationId = await ctx.db.insert("conversations", {
          userId,
          title: `${title} (Imported)`,
          lastMessageAt: createdAt,
          currentBranch: "main",
        });

        // Create main branch
        await ctx.db.insert("conversationBranches", {
          conversationId,
          branchId: "main",
          title: "Main",
          createdAt,
          isActive: true,
        });

        // Import messages
        for (let i = 0; i < thread.messages.length; i++) {
          const message = thread.messages[i];
          await ctx.db.insert("messages", {
            conversationId,
            branchId: "main",
            parentMessageId: i > 0 ? undefined : undefined,
            role: message.role === "model" ? "assistant" : message.role,
            content: message.content,
          });
        }

        importedCount++;
      }

      return { importedConversations: importedCount };
    }

    if ("conversations" in exportData) {
      // Handle bulk export format
      let importedCount = 0;

      for (const conv of exportData.conversations) {
        const conversationId = await ctx.db.insert("conversations", {
          userId,
          title: `${conv.conversation.title} (Imported)`,
          lastMessageAt: conv.conversation.lastMessageAt,
          currentBranch: conv.conversation.currentBranch ?? "main",
        });

        // Import branches
        const branchIdMap = new Map<string, string>();
        for (const branch of conv.branches ?? []) {
          const newBranchId = await ctx.db.insert("conversationBranches", {
            conversationId,
            branchId: branch.branchId,
            parentBranchId: branch.parentBranchId,
            branchPoint: undefined,
            title: branch.title,
            createdAt: branch.createdAt,
            isActive: branch.isActive,
          });
          branchIdMap.set(branch._id, newBranchId);
        }

        // Import messages
        const messageIdMap = new Map<string, string>();
        for (const message of conv.messages ?? []) {
          const newMessageId = await ctx.db.insert("messages", {
            conversationId,
            branchId: message.branchId,
            parentMessageId: message.parentMessageId
              ? (messageIdMap.get(message.parentMessageId) as any)
              : undefined,
            role: message.role,
            content: message.content,
            thinking: message.thinking,
            attachments: message.attachments,
            toolCalls: message.toolCalls,
            toolCallId: message.toolCallId,
            generationMetrics: message.generationMetrics,
            isEdited: message.isEdited,
            editedAt: message.editedAt,
            isError: message.isError,
            canvasData: message.canvasData,
          });
          messageIdMap.set(message._id, newMessageId);
        }

        importedCount++;
      }

      return { importedConversations: importedCount };
    }

    // Handle our native single conversation format
    if ("conversation" in exportData && "branches" in exportData) {
      // Validate export data version
      if (exportData.version && exportData.version !== "1.0") {
        throw new Error("Unsupported export data version");
      }

      // Create new conversation
      const conversationId = await ctx.db.insert("conversations", {
        userId,
        title: `${exportData.conversation.title} (Imported)`,
        lastMessageAt: exportData.conversation.lastMessageAt,
        currentBranch: exportData.conversation.currentBranch ?? "main",
      });

      // Import branches (first pass - without branchPoint references)
      const branchIdMap = new Map<string, string>();
      for (const branch of exportData.branches) {
        const newBranchId = await ctx.db.insert("conversationBranches", {
          conversationId,
          branchId: branch.branchId,
          parentBranchId: branch.parentBranchId,
          branchPoint: undefined, // Will be updated after messages are imported
          title: branch.title,
          createdAt: branch.createdAt,
          isActive: branch.isActive,
        });
        branchIdMap.set(branch._id, newBranchId);
      }

      // Import messages
      const messageIdMap = new Map<string, string>();
      for (const message of exportData.messages) {
        const newMessageId = await ctx.db.insert("messages", {
          conversationId,
          branchId: message.branchId,
          parentMessageId: message.parentMessageId
            ? (messageIdMap.get(message.parentMessageId) as any)
            : undefined,
          role: message.role,
          content: message.content,
          thinking: message.thinking,
          attachments: message.attachments,
          toolCalls: message.toolCalls,
          toolCallId: message.toolCallId,
          generationMetrics: message.generationMetrics,
          isEdited: message.isEdited,
          editedAt: message.editedAt,
          isError: message.isError,
          canvasData: message.canvasData,
        });
        messageIdMap.set(message._id, newMessageId);
      }

      return conversationId;
    }

    throw new Error("Unsupported export format");
  },
});

export const bulkExportConversations = query({
  args: { conversationIds: v.array(v.id("conversations")) },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const exports = [];
    for (const conversationId of args.conversationIds) {
      const conversation = await ctx.db.get(conversationId);
      if (!conversation || conversation.userId !== userId) {
        continue; // Skip conversations user doesn't own
      }

      // Get branches and messages for this conversation
      const branches = await ctx.db
        .query("conversationBranches")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversationId)
        )
        .collect();

      const messages = await ctx.db
        .query("messages")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversationId)
        )
        .collect();

      exports.push({
        version: "1.0",
        exportedAt: Date.now(),
        conversation: {
          title: conversation.title,
          lastMessageAt: conversation.lastMessageAt,
          currentBranch: conversation.currentBranch,
        },
        branches,
        messages: messages.map((msg) => ({
          ...msg,
          conversationId: undefined,
        })),
      });
    }

    return {
      version: "1.0",
      exportedAt: Date.now(),
      conversations: exports,
    };
  },
});

export const exportAllConversations = query({
  args: {},
  handler: async (ctx, _args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get all conversations for the user
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const exports = [];
    for (const conversation of conversations) {
      // Get branches and messages for this conversation
      const branches = await ctx.db
        .query("conversationBranches")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversation._id)
        )
        .collect();

      const messages = await ctx.db
        .query("messages")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversation._id)
        )
        .collect();

      exports.push({
        version: "1.0",
        exportedAt: Date.now(),
        conversation: {
          title: conversation.title,
          lastMessageAt: conversation.lastMessageAt,
          currentBranch: conversation.currentBranch,
        },
        branches,
        messages: messages.map((msg) => ({
          ...msg,
          conversationId: undefined,
        })),
      });
    }

    return {
      version: "1.0",
      exportedAt: Date.now(),
      totalConversations: exports.length,
      conversations: exports,
    };
  },
});

export const searchConversations = query({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const limit = args.limit ?? 15;

    // -----------------------
    // 1. Title substring match (legacy)
    // -----------------------
    const allConvs = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(200);

    const substringMatches = allConvs.filter((conv) =>
      conv.title.toLowerCase().includes(args.query.toLowerCase())
    );

    // -----------------------
    // 2. Semantic search with Vectorize
    // -----------------------
    let semanticConversationIds: Array<Id<"conversations">> = [];
    try {
      // Call the internal action which performs the Vectorize query
      let vectorMatches: Array<{ _id: Id<"messages">; score: number }> = [];
      if (typeof (ctx as any).runAction === "function") {
        try {
          vectorMatches = await (ctx as any).runAction(
            (api as any).vectorize.searchContext,
            {
              userId: userId as any,
              query: args.query,
              topK: 50,
            }
          );
        } catch (err) {
          console.warn("Vectorize semantic search failed", err);
        }
      }

      // Map to conversations (dedupe while keeping highest score)
      const scoreByConv = new Map<Id<"conversations">, number>();
      for (const match of vectorMatches) {
        const msg: any = await ctx.db.get(match._id);
        if (!msg) continue;
        const cid = msg.conversationId;
        const prev = scoreByConv.get(cid) ?? -Infinity;
        if (match.score > prev) {
          scoreByConv.set(cid, match.score);
        }
      }
      semanticConversationIds = Array.from(scoreByConv.keys());

      // Sort semantic IDs by descending score
      semanticConversationIds.sort(
        (a, b) => (scoreByConv.get(b) ?? 0) - (scoreByConv.get(a) ?? 0)
      );
    } catch (err) {
      // If Vectorize is not configured or errors, silently fall back
      console.warn("Vectorize semantic search failed", err);
    }

    // -----------------------
    // 3. Combine & dedupe, prioritising semantic results then substring
    // -----------------------
    const combinedIds: Array<Id<"conversations">> = [];
    const addUnique = (arr: Array<Id<"conversations">>) => {
      for (const id of arr) {
        if (!combinedIds.includes(id)) combinedIds.push(id);
      }
    };
    addUnique(semanticConversationIds);
    addUnique(substringMatches.map((c) => c._id));

    // Cap to 'limit' ids
    const finalIds = combinedIds.slice(0, limit);

    // Fetch conversation docs & compute message counts
    const results = await Promise.all(
      finalIds.map(async (cid) => {
        const conv: any = await ctx.db.get(cid);
        if (!conv) return null;
        const currentBranchId = conv.currentBranch ?? "main";
        const messageCount = await ctx.db
          .query("messages")
          .withIndex("by_conversation_branch", (q) =>
            q.eq("conversationId", cid).eq("branchId", currentBranchId)
          )
          .collect()
          .then((msgs) => msgs.length);
        return { ...conv, messageCount };
      })
    );

    // Filter nulls (in case some convs got deleted in between)
    const filtered = results.filter((r): r is NonNullable<typeof r> =>
      Boolean(r)
    );

    // -----------------------
    // 4. Order results
    //    - If this is an active search (non-empty query), preserve the order
    //      derived from semantic → substring matches, while still surfacing
    //      pinned conversations at the very top.
    //    - If the query is empty we fall back to the previous behaviour of
    //      pinned first then recency.
    // -----------------------
    const isActiveSearch = args.query.trim() !== "";

    if (isActiveSearch) {
      const convById = new Map(filtered.map((c) => [c._id, c]));

      // Preserve original ranking order from `finalIds`
      let ordered = finalIds
        .map((id) => convById.get(id))
        .filter((c): c is NonNullable<typeof c> => Boolean(c));

      // Move pinned conversations to the very top (maintaining their relative order)
      const pinned = ordered.filter((c) => c.isPinned);
      const nonPinned = ordered.filter((c) => !c.isPinned);
      ordered = [...pinned, ...nonPinned];

      return ordered;
    }

    // Legacy ordering for empty query: pinned first, then lastMessageAt (desc)
    const sorted = filtered.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return b.lastMessageAt - a.lastMessageAt;
    });

    return sorted;
  },
});

export const migrateCounts = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    for (const conv of conversations) {
      const branches = await ctx.db
        .query("conversationBranches")
        .withIndex("by_conversation", (q) => q.eq("conversationId", conv._id))
        .collect();

      for (const branch of branches) {
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_conversation_branch", (q) =>
            q.eq("conversationId", conv._id).eq("branchId", branch.branchId)
          )
          .collect();
        const count = messages.length;
        const lastTime =
          count > 0
            ? Math.max(...messages.map((m) => m._creationTime))
            : branch.createdAt;
        await ctx.db.patch(branch._id, {
          messageCount: count,
          lastMessageAt: lastTime,
        });
      }

      const currentBranchId = conv.currentBranch ?? "main";
      const currentBranch = branches.find(
        (b) => b.branchId === currentBranchId
      );
      if (currentBranch) {
        await ctx.db.patch(conv._id, {
          messageCount: currentBranch.messageCount ?? 0,
          lastMessageAt: currentBranch.lastMessageAt ?? Date.now(),
        });
      }
    }

    return { migrated: conversations.length };
  },
});
