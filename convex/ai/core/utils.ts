"use node";

import { generateText } from "ai";
import { api } from "../../_generated/api";
import { createAIModel, getProviderApiKey } from "../providers";
import {
  SUPPORTED_PROVIDERS,
  PROVIDER_BASE_URLS,
} from "../providers/constants";
import { getBestAvailableProvider } from "../../../src/lib/models";

export async function generateTitle(
  ctx: any,
  userMessage: string
): Promise<string> {
  const provider = "groq";
  const model = "meta-llama/llama-4-scout-17b-16e-instruct";

  // Get user's API key for Groq
  const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
    provider,
  });

  const { apiKey, usingUserKey } = getProviderApiKey(provider, apiKeyRecord);

  if (!apiKey) {
    throw new Error(
      `No API key available for ${provider}. ${!usingUserKey ? "Add your own Groq API key in settings for unlimited usage." : ""}`
    );
  }

  const { model: titleAiModel } = createAIModel({
    provider,
    model,
    apiKey,
    baseURL: PROVIDER_BASE_URLS[provider],
    skipMiddleware: true,
  });

  const result = await generateText({
    model: titleAiModel,
    messages: [
      {
        role: "system",
        content: `You are an expert at generating concise, informative conversation titles. Your task is to analyze the user's message and produce a clear, specific title that accurately summarizes the main topic or request.

  Guidelines:
  - Title must be 3-7 words long.
  - Be highly specific and descriptive; capture the essence of the conversation.
  - Use strong nouns and verbs; avoid vague or generic words.
  - Do not include filler words, quotes, punctuation, or special characters.
  - Make the title immediately understandable and relevant.
  - Avoid repeating the user's question format; instead, summarize the subject.
  - Do not include "Chat", "Conversation", or similar generic terms.

  Examples:
  User: "How do I fix this Python error?" → Python Error Troubleshooting
  User: "I need recipe ideas for dinner" → Creative Dinner Recipe Ideas
  User: "Explain quantum physics concepts" → Quantum Physics Concepts Overview
  User: "Help me plan a trip to Japan" → Japan Trip Planning Guide
  User: "What's the weather like today?" → Today Weather Forecast
  User: "Can you write a story about dragons?" → Dragon Story Writing
  User: "I'm having relationship problems" → Relationship Advice and Support
  User: "How to optimize my code performance?" → Code Performance Optimization Tips

  Respond with ONLY the title. Do not include any explanations, greetings, or extra text.`
      },
      {
        role: "user",
        content: userMessage.slice(0, 2000)
      },
    ],
    temperature: 0.3,
    maxOutputTokens: 150,
  });

  // Clean up the response and ensure it's good
  let title = result.text
    .trim()
    .replace(/['""`]/g, "") // Remove quotes and backticks
    .replace(/[^\w\s-]/g, "") // Remove special characters except spaces and hyphens
    .split(" ")
    .filter((word) => word.length > 0) // Remove empty strings
    .slice(0, 7) // Max 7 words
    .join(" ");

  // Ensure title is meaningful and not too generic
  if (
    !title ||
    title.length < 3 ||
    !!/^(chat|help|question|talk|conversation)$/.exec(title.toLowerCase())
  ) {
    // Try to extract keywords from user message as fallback
    const words = userMessage
      .toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter(
        (word) =>
          word.length > 3 &&
          ![
            "what",
            "how",
            "can",
            "you",
            "help",
            "please",
            "with",
            "about",
            "would",
            "could",
            "should",
          ].includes(word)
      );

    if (words.length > 0) {
      title = words
        .slice(0, 3)
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    } else {
      title = "New Chat";
    }
  }

  return title || "New Chat";
}

export async function getAvailableProviders(ctx: any): Promise<string[]> {
  const availableProviders: string[] = [];

  // Get user's API keys
  const userApiKeys = await ctx.runQuery(api.apiKeys.list);

  for (const provider of SUPPORTED_PROVIDERS) {
    let hasApiKey = false;

    // Check if user has API key in database
    const userApiKey = userApiKeys.find(
      (key: any) => key.provider === provider && key.hasKey
    );
    if (userApiKey) {
      hasApiKey = true;
    } else {
      // Check if there's a built-in API key in environment variables
      const { apiKey } = getProviderApiKey(provider);
      hasApiKey = !!apiKey;
    }

    if (hasApiKey) {
      availableProviders.push(provider);
    }
  }

  // Add custom providers
  try {
    const customProviders = await ctx.runQuery(api.customProviders.list);
    for (const customProvider of customProviders) {
      if (customProvider.isActive) {
        availableProviders.push(customProvider.name);
      }
    }
  } catch (error) {
    // Ignore if custom providers table doesn't exist or other errors
    console.warn("Failed to fetch custom providers:", error);
  }

  // Always include system provider
  availableProviders.push("system");

  return availableProviders;
}

export async function classifyTask(
  ctx: any,
  userMessage: string
): Promise<{ model: string; provider: string }> {
  const classifierProvider = "google";
  const classifierModel = "gemini-2.0-flash-lite";

  const { apiKey } = getProviderApiKey(classifierProvider);

  if (!apiKey) {
    console.warn("No API key for classifier, using default model");
    return { model: "gemini-2.5-flash-preview-05-20", provider: "google" };
  }

  const { model: classifierAiModel } = createAIModel({
    provider: classifierProvider,
    model: classifierModel,
    apiKey,
    baseURL: PROVIDER_BASE_URLS[classifierProvider],
  });

  const prompt = `Classify the following user query into one of these categories:
- simple_fast: for simple, quick questions or tasks that don't require much context or reasoning
- large_context: for requests involving large amounts of text, long contexts, or complex analysis of big data
- general_code: for general questions, coding tasks, programming help, or technical explanations
- creative_writing: for creative writing, storytelling, content generation, or artistic tasks

Query: ${userMessage.slice(0, 2000)}

Respond with ONLY the category name.`;

  try {
    const result = await generateText({
      model: classifierAiModel,
      prompt,
      temperature: 0.1,
      maxOutputTokens: 50,
    });

    const category = result.text.trim().toLowerCase();

    const categoryMap: Record<
      string,
      { model: string; defaultProvider: string }
    > = {
      simple_fast: {
        model: "gemini-2.5-flash-preview-05-20",
        defaultProvider: "google",
      },
      large_context: { model: "gemini-2.5-pro", defaultProvider: "google" },
      general_code: { model: "o4-mini", defaultProvider: "openai" },
      creative_writing: { model: "gpt-4.1-mini", defaultProvider: "openai" },
    };

    const selected = categoryMap[category] || categoryMap.simple_fast;
    const bestProvider =
      getBestAvailableProvider(selected.model, [])?.provider ||
      selected.defaultProvider;

    return { model: selected.model, provider: bestProvider };
  } catch (error) {
    console.error("Classification failed:", error);
    return { model: "gemini-2.5-flash-preview-05-20", provider: "google" };
  }
}
