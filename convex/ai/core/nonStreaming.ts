"use node";

import { generateText } from "ai";
import { api } from "../../_generated/api";
import { createAvailableTools } from "../tools";
import {
  createAIModel,
  getProviderApiKey,
  getDefaultModel,
  getModelInfo,
  SupportedProvider,
} from "../providers";
import {
  getModelProviders,
  getBestAvailableProvider,
  getPrioritizedProviders,
} from "../../../src/lib/models";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { getUserFriendlyErrorMessage } from "../utils/errors";
import { classifyTask } from "./utils";

export async function generateNonStreamingResponse(
  ctx: any,
  args: {
    conversationId: string;
    branchId?: string;
    messages: Array<{
      role: string;
      content:
        | string
        | Array<{
            type: "text" | "image" | "file";
            text?: string;
            image?: string;
            file?: string;
            data?: string;
            mimeType?: string;
          }>;
    }>;
    provider?: string;
    model?: string;
    temperature?: number;
    enabledTools?: string[];
    thinkingBudget?: string | number;
    persona?: string;
    recipe?: string;
  }
) {
  let originalProvider = (args.provider as SupportedProvider) || "openai";
  let model = args.model ?? getDefaultModel(originalProvider);
  const temperature = args.temperature ?? 1;
  const enabledTools = args.enabledTools ?? [];
  const branchId = args.branchId;
  const personaId = args.persona || "none";
  const recipeId = args.recipe || "none";

  // After model assignment
  if (model === "auto") {
    const classified = await classifyTask(
      ctx,
      args.messages[args.messages.length - 1].content as string
    );
    model = classified.model;
    originalProvider = classified.provider as SupportedProvider;
  }

  // Get prioritized providers for this model, with user's preferred provider first if specified
  const providersToTry = getPrioritizedProviders(model, args.provider);

  // Filter out any "unknown" providers and validate we have valid providers
  const validProvidersToTry = providersToTry.filter(
    (p) => p.provider !== "unknown"
  );

  if (validProvidersToTry.length === 0) {
    // No valid providers found, create error message
    const errorMessage = `Model "${model}" is not supported by any available providers. Please check the model name or try a different model.`;

    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content: errorMessage,
      generationMetrics: {
        provider: "unknown",
        model: model,
        generationTimeMs: 0,
        attemptedProviders: ["unknown"],
        fallbackUsed: false,
      },
      isError: true,
    });

    return {
      error: errorMessage,
      usingUserKey: false,
      generationMetrics: {
        provider: "unknown",
        model: model,
        generationTimeMs: 0,
        attemptedProviders: ["unknown"],
        fallbackUsed: false,
      },
    };
  }

  let lastError: Error | null = null;
  const attemptedProviders: string[] = [];
  let actualProvider: string = validProvidersToTry[0].provider;
  let actualModelId: string = validProvidersToTry[0].modelId;

  // Check if the model supports tools early to determine prompt structure
  const modelInfo = getModelInfo(model);
  const modelSupportsTools = modelInfo.supportsTools;

  let usingUserKey = false;
  let abortController: AbortController | null = null;

  // Provider fallback loop
  for (
    let providerIndex = 0;
    providerIndex < validProvidersToTry.length;
    providerIndex++
  ) {
    const currentProviderInfo = validProvidersToTry[providerIndex];
    actualProvider = currentProviderInfo.provider;
    actualModelId = currentProviderInfo.modelId;

    console.log(
      `[Provider Fallback] Attempting provider ${actualProvider} (${providerIndex + 1}/${validProvidersToTry.length}) for model ${model}`
    );

    try {
      // Check if this is a custom provider
      let customProvider = null;
      if (
        typeof actualProvider === "string" &&
        ![
          "openai",
          "anthropic",
          "google",
          "openrouter",
          "groq",
          "deepseek",
          "grok",
          "cohere",
          "mistral",
          "cerebras",
          "github",
        ].includes(actualProvider)
      ) {
        // This is a custom provider, fetch its configuration
        customProvider = await ctx.runQuery(api.customProviders.getByName, {
          name: actualProvider,
        });
        if (!customProvider) {
          const error = new Error(
            `Custom provider '${actualProvider}' not found`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Custom provider ${actualProvider} not found, trying next provider`
          );
          continue;
        }
      }

      // Create an AbortController for timeout handling
      abortController = new AbortController();

      // Set timeout based on provider and model - Deep research models need much longer timeouts
      let timeoutMs = 300000; // Default 5 minutes
      if (actualProvider === "google") {
        timeoutMs = 600000; // 10 minutes for Google
      }
      if (actualModelId.includes("deep-research")) {
        timeoutMs = 3600000; // 1 hour for deep research models
      }
      const timeoutId = setTimeout(() => {
        if (abortController && !abortController.signal.aborted) {
          abortController.abort(
            new Error(`Request timeout after ${timeoutMs / 1000} seconds`)
          );
        }
      }, timeoutMs);

      // Process multimodal content if needed
      const processedMessages = await Promise.all(
        args.messages.map(async (message) => {
          if (Array.isArray(message.content)) {
            const processedContent = await Promise.all(
              message.content.map(async (part) => {
                if (part.type === "image") {
                  const imageValue = part.image;

                  // Detect Convex storage IDs if the value is not already an http(s) or data URI
                  const isStorageId =
                    typeof imageValue === "string" &&
                    !imageValue.startsWith("http") &&
                    !imageValue.startsWith("data:");

                  if (isStorageId) {
                    try {
                      const publicUrl = await ctx.storage.getUrl(
                        imageValue as any
                      );
                      if (publicUrl) {
                        return {
                          type: "image",
                          image: publicUrl,
                        };
                      } else {
                        console.warn(
                          `Failed to get public URL for storage ID: ${imageValue}`
                        );
                        return part;
                      }
                    } catch (error) {
                      console.error(
                        `Error getting public URL for storage ID ${imageValue}:`,
                        error
                      );
                      return part;
                    }
                  }

                  return part;
                } else if (part.type === "file") {
                  const fileValue =
                    (part as any).data ??
                    (part as any).file?.data ??
                    (part as any).file;

                  // Treat as storage ID if not a URL/data URI
                  const isStorageId =
                    typeof fileValue === "string" &&
                    !fileValue.startsWith("http") &&
                    !fileValue.startsWith("data:");

                  if (isStorageId) {
                    try {
                      // Get file metadata using the system table (recommended approach)
                      const fileMetadata = await ctx.db.system.get(fileValue);
                      const publicUrl = await ctx.storage.getUrl(fileValue);

                      if (publicUrl && fileMetadata) {
                        return {
                          type: "file",
                          data: publicUrl,
                          mediaType:
                            fileMetadata.contentType ??
                            "application/octet-stream",
                        };
                      } else {
                        console.warn(
                          `Failed to get public URL or metadata for file storage ID: ${fileValue}`
                        );
                        return part;
                      }
                    } catch (error) {
                      console.error(
                        `Error getting public URL for file storage ID ${fileValue}:`,
                        error
                      );
                      return part;
                    }
                  }

                  return part;
                }
                return part;
              })
            );
            return { ...message, content: processedContent };
          }
          return message;
        })
      );

      // Get API Key for the selected provider
      let apiKey: string;
      if (customProvider) {
        // For custom providers, use the API key stored in the custom provider configuration
        apiKey = customProvider.apiKey;
        usingUserKey = true;
      } else {
        const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
          provider: actualProvider,
        });

        const { apiKey: retrievedApiKey, usingUserKey: userKeyFlag } =
          getProviderApiKey(actualProvider as SupportedProvider, apiKeyRecord);
        apiKey = retrievedApiKey;
        usingUserKey = userKeyFlag;
      }

      if (!apiKey) {
        const error = new Error(
          `No API key available for ${actualProvider}. Please configure your API key in settings or use a provider with built-in support.`
        );
        lastError = error;
        attemptedProviders.push(actualProvider);
        console.log(
          `[Provider Fallback] No API key for ${actualProvider}, trying next provider`
        );
        continue;
      }

      // Only check usage limits if using built-in keys
      if (!usingUserKey && actualProvider !== "openrouter") {
        // Estimate token usage for credit check (rough estimate)
        const estimatedInputTokens = processedMessages.reduce((total, msg) => {
          if (typeof msg.content === "string") {
            return total + Math.ceil(msg.content.length / 4); // Rough estimate: 4 chars per token
          }
          // Handle array content
          const textContent = msg.content
            .filter(
              (part) =>
                typeof part === "object" &&
                part !== null &&
                "type" in part &&
                part.type === "text"
            )
            .map((part) => {
              // Safe to cast since we've filtered for text type
              const textPart = part as { type: "text"; text?: string };
              return textPart.text || "";
            })
            .join(" ");
          return total + Math.ceil(textContent.length / 4);
        }, 0);
        const estimatedOutputTokens = 2000; // Conservative estimate

        const creditCheck = await ctx.runQuery(
          api.usage.checkCreditsAvailable,
          {
            model: actualModelId,
            estimatedInputTokens,
            estimatedOutputTokens,
            provider: actualProvider,
          }
        );

        if (!creditCheck.hasCredits) {
          const error = new Error(
            `Insufficient credits. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.availableCredits}. Add your own API keys in settings for unlimited usage.`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Insufficient credits for ${actualProvider}, trying next provider`
          );
          continue;
        }

        if (creditCheck.wouldExceedSpending) {
          const error = new Error(
            `This request would exceed your monthly spending limit. Add your own API keys in settings for unlimited usage.`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Would exceed spending limit for ${actualProvider}, trying next provider`
          );
          continue;
        }
      }

      // Get MCP servers for the user
      const { getAuthUserId } = await import("@convex-dev/auth/server");
      const userId = await getAuthUserId(ctx);
      let mcpServers: any[] = [];
      let n8nServers: any[] = [];
      let n8nWorkflows: any[] = [];

      if (userId) {
        mcpServers = await ctx.runQuery(api.mcpServers.listEnabled, {});
        // Load server-based n8n configuration
        n8nServers = await ctx.runQuery(api.n8nServers.listEnabled, {});
        // Legacy workflow-based configuration (optional)
        n8nWorkflows = await ctx
          .runQuery(api.n8nWorkflows.listEnabled, {})
          .catch(() => []);
      }

      // Create tools based on enabled tools and model capabilities
      const availableTools = await createAvailableTools(
        ctx,
        enabledTools,
        model,
        usingUserKey,
        mcpServers,
        n8nServers,
        n8nWorkflows
      );

      // Check if we have tools to add
      const hasTools = Object.keys(availableTools).length > 0;

      // Get user preferences and instructions to build system prompt
      const userPreferences = await ctx.runQuery(api.preferences.get);
      const userInstructions = await ctx.runQuery(api.userInstructions.get);

      // Prepare messages with system prompt if enabled
      let messagesWithSystemPrompt = [...processedMessages];

      // Check if there's already a system message
      const hasSystemMessage = processedMessages.some(
        (msg) => msg.role === "system"
      );

      if (!hasSystemMessage) {
        let systemContent = "";

        // Include today's date in the system prompt (human-readable format)
        const today = new Date();
        const todayDate = today.toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        });

        if (
          userPreferences?.useCustomSystemPrompt &&
          userPreferences?.systemPrompt
        ) {
          // Use custom system prompt
          systemContent =
            `Current date: ${todayDate}\n\n` + userPreferences.systemPrompt;
        } else {
          // Use default system prompt when custom is disabled
          if (modelSupportsTools) {
            systemContent =
              `Current date: ${todayDate}\n\n` +
              "You are ErzenAI — a world-class autonomous AI agent. You think step-by-step, plan before acting, and judiciously call external tools to accomplish the user's goals.\n\n" +
              "### Core Principles\n" +
              "- **Outcome Focused**: Always deliver useful, actionable results.\n" +
              "- **Ethical & Safe**: Respect privacy, honesty, and safety.\n" +
              "- **Continuous Learning**: Reflect on each answer and improve.\n\n" +
              "### Working Method\n" +
              "1. **Plan first**: Draft a concise plan in your native thinking channel before responding.\n" +
              "2. **Tool usage**: Tell the user which tool you will use and why, then invoke it with precise arguments.\n" +
              "3. **Reflect**: Inspect tool output, decide if more actions are required, iterate.\n" +
              "4. **Deliver**: Craft a clear, helpful answer.\n\n" +
              "### Markdown Style Guide\n" +
              "- Put **one blank line** after every heading, paragraph, list or code block.\n" +
              "- Use ### (h3) or smaller headers; never # or ##.\n" +
              "- Use **bold** and *italics* sparingly for emphasis.\n" +
              "- Wrap code in fenced blocks with language tags and a blank line before and after.\n" +
              "- Use - bullets or 1. numbered lists with spacing.\n" +
              "- Insert horizontal rules (---) to separate major sections.\n" +
              "- Keep lines short (≤100 characters) and avoid trailing spaces.\n\n" +
              "Respond in the same language as the user and favour clarity over verbosity.";
          } else {
            systemContent =
              `Current date: ${todayDate}\n\n` +
              "You are ErzenAI, an intelligent AI assistant that reasons step-by-step and delivers clear, well-structured answers.\n\n" +
              "### Core Principles\n" +
              "- **Outcome Focused**: Provide practical, actionable information.\n" +
              "- **Ethical & Safe**: Be truthful, respectful and protect user privacy.\n" +
              "- **Continuous Learning**: Reflect on feedback and improve.\n\n" +
              "### Working Method\n" +
              "1. **Plan first**: Briefly outline your answer in the native thinking channel.\n" +
              "2. **Research**: Rely on internal knowledge and logical reasoning.\n" +
              "3. **Deliver**: Craft a concise, helpful response.\n\n" +
              "### Markdown Style Guide\n" +
              "- Put **one blank line** after every heading, paragraph, list or code block.\n" +
              "- Use ### (h3) or smaller headers; never # or ##.\n" +
              "- Use **bold** and *italics* sparingly.\n" +
              "- Wrap code in fenced blocks with language tags.\n" +
              "- Use - bullets or 1. numbered lists with spacing.\n" +
              "- Insert horizontal rules (---) to separate sections.\n" +
              "- Keep lines short (≤100 characters) and avoid trailing spaces.\n\n" +
              "Respond in the same language as the user and favour clarity over verbosity.";
          }
        }

        // Append guidelines for Mermaid diagrams and language adaptation
        systemContent +=
          "\n\n## Diagrams and Language\n- When presenting charts or diagrams, embed them using Mermaid syntax inside ```mermaid``` code blocks.\n- Always respond in the same language that the user used in their last message.";

        // Append persona prompt if provided
        const PERSONA_PROMPTS: Record<string, string> = {
          companion:
            "You are the user's compassionate companion. Respond with warmth, empathy and encouragement. Prioritise emotional support over facts.",
          friend:
            "You are the user's close friend. Keep the tone casual, supportive and lightly humorous. Use informal language, contractions and emojis when appropriate.",
          comedian:
            "You are a stand-up comedian. Deliver responses with wit and humour while still addressing the user's topic. Make sure jokes are light-hearted and never offensive.",
          not_a_doctor:
            "You are NOT a medical professional. If the user requests medical advice you must disclaim you are not a doctor and encourage consulting a qualified physician. Provide general information only.",
          not_a_therapist:
            "You are NOT a mental-health professional. Provide supportive, non-clinical responses and encourage the user to seek professional help for serious issues.",
        };

        if (personaId !== "none" && PERSONA_PROMPTS[personaId]) {
          systemContent = PERSONA_PROMPTS[personaId] + "\n\n" + systemContent;
        }

        // Append recipe prompt if provided
        const RECIPE_PROMPTS: Record<string, string> = {
          summarise:
            "Whenever the user provides text, summarise it concisely using bullet-points. If the text is short, provide a one-sentence summary.",
          translate_es:
            "Translate all user input into Spanish. Respond ONLY with the translation, no explanations.",
          brainstorm:
            "Generate a creative list of at least 10 varied ideas that satisfy the user's request. Encourage originality and diversity.",
          email_draft:
            "Craft a professional, well-structured email based on the user's instructions. Use a polite tone and clear formatting.",
          agent:
            "You are an autonomous multi-tool AI agent, equipped with specialised tools such as **thinking** (for deep internal reasoning) and **plan** (for orchestrating multi-step workflows).  When `agent` mode is active you must:\n\n1. **Analyse** the user’s request and decompose it into clear, actionable TODO items.\n2. **Create a high-level execution strategy** *by calling the `plan` tool first* whenever the task involves three or more steps or multiple tools.  Present the resulting plan to the user under a heading `### Plan` using markdown check-boxes (`- [ ]`).\n3. **Iteratively execute** each TODO in order.  Call external tools whenever they add value.  For complex reasoning within a step, explicitly invoke the `thinking` tool to think step-by-step before acting.\n4. **Reflect & update**: After each tool call, summarise the result, tick off the completed TODO with `- [x]`, and adjust the remaining checklist if necessary.\n5. **Finish strong**: When all TODOs are done, deliver a polished answer, including any deliverables, and mark the plan as ✓ **Done**.\n\nGuidelines:\n• Use the `thinking` tool liberally for challenging or ambiguous steps to ensure robust reasoning.\n• Use the `plan` tool whenever orchestrating multiple tools or when the user’s goal lacks a clear path.\n• Keep tool arguments precise and only include the parameters defined in each tool’s JSON schema.\n• Communicate progress to the user, but keep internal reasoning inside `thinking` tool calls.\n• Strive for efficiency—avoid unnecessary tool calls or over-long responses.\n\nRemember: iterate, reflect, and adapt until the task is fully solved.",
        };

        if (recipeId !== "none" && RECIPE_PROMPTS[recipeId]) {
          systemContent += "\n\n" + RECIPE_PROMPTS[recipeId];
        }

        // Add user instructions if available
        if (userInstructions && userInstructions.trim()) {
          systemContent +=
            "\n\nAdditional user instructions:\n" + userInstructions;
        }

        // Add system prompt at the beginning
        messagesWithSystemPrompt = [
          {
            role: "system" as const,
            content: systemContent,
          },
          ...processedMessages,
        ];
      }

      // Create AI model instance with native thinking support
      const { model: nonStreamingAiModel, providerOptions } = createAIModel({
        provider: actualProvider,
        model: actualModelId,
        apiKey,
        baseURL: customProvider
          ? customProvider.baseURL
          : PROVIDER_BASE_URLS[
              actualProvider as keyof typeof PROVIDER_BASE_URLS
            ],
        temperature,
        thinkingBudget: args.thinkingBudget,
        enabledTools,
        customProvider: customProvider
          ? {
              name: customProvider.name,
              baseURL: customProvider.baseURL,
              models: customProvider.models,
            }
          : undefined,
      });

      // Track generation start time
      const generationStartTime = Date.now();

      // Generate the response using generateText - conditionally include tools
      const generateTextConfig: any = {
        model: nonStreamingAiModel,
        messages: messagesWithSystemPrompt as any,
        temperature,
        maxSteps: 35,
        abortSignal: abortController.signal,
        // Include native thinking support via provider options
        providerOptions,
      };

      // Only add tools if model supports them AND we have tools to add
      if (modelSupportsTools && hasTools) {
        generateTextConfig.tools = availableTools;
      }

      // Enhanced error handling for SDK v5 generateText call
      let result;
      try {
        result = await generateText(generateTextConfig);
      } catch (generateError) {
        // Handle SDK v5 specific errors that might be thrown during generation
        console.error(
          `[SDK v5 Generation Error] Error during generateText for ${actualProvider}/${actualModelId}:`,
          generateError
        );

        // Interface for extended error types that may include additional properties from AI SDK v5 libraries
        interface ExtendedError extends Error {
          data?: {
            error?: {
              message?: string;
              type?: string;
              code?: string;
            };
          };
          responseBody?: string;
          code?: string;
          type?: string;
          status?: number;
          statusText?: string;
          // SDK v5 specific error properties
          cause?: any;
          details?: any;
          response?: {
            status?: number;
            statusText?: string;
            data?: any;
          };
        }

        // Helper function to extract error message from various nested locations (SDK v5 compatible)
        const getErrorMessage = (error: any): string => {
          if (typeof error === "string") {
            return error;
          }
          if (error instanceof Error) {
            const extendedError = error as ExtendedError;

            // Check multiple possible locations for error messages in SDK v5
            return (
              error.message ||
              extendedError.data?.error?.message ||
              extendedError.cause?.message ||
              extendedError.details?.message ||
              extendedError.response?.data?.error?.message ||
              extendedError.responseBody ||
              extendedError.statusText ||
              String(error)
            );
          }
          return String(error);
        };

        // Enhanced error type detection for SDK v5
        const getErrorType = (error: any): string => {
          if (typeof error === "object" && error !== null) {
            const extendedError = error as ExtendedError;
            return (
              extendedError.type ||
              extendedError.code ||
              extendedError.data?.error?.type ||
              extendedError.data?.error?.code ||
              "unknown"
            );
          }
          return "unknown";
        };

        // Check if this is a specific SDK v5 error format
        if (generateError && typeof generateError === "object") {
          const sdkError = generateError as ExtendedError;

          // Handle specific SDK v5 error types
          if (
            sdkError.type === "AI_APICallError" ||
            sdkError.name === "AI_APICallError"
          ) {
            console.log(
              `[SDK v5] API call error detected: ${getErrorMessage(sdkError)}`
            );
          } else if (
            sdkError.type === "AI_InvalidPromptError" ||
            sdkError.name === "AI_InvalidPromptError"
          ) {
            console.log(
              `[SDK v5] Invalid prompt error detected: ${getErrorMessage(sdkError)}`
            );
          } else if (
            sdkError.type === "AI_NoSuchModelError" ||
            sdkError.name === "AI_NoSuchModelError"
          ) {
            console.log(
              `[SDK v5] Model not found error detected: ${getErrorMessage(sdkError)}`
            );
          }
        }

        // Re-throw to be handled by the existing provider fallback logic
        throw generateError;
      }

      // ------------------------------------------------------------------
      // Calculate additional built-in tool costs (per-call flat fees)
      // ------------------------------------------------------------------
      let extraToolDollars = 0;
      if (result.toolCalls && Array.isArray(result.toolCalls)) {
        const modelLower = model.toLowerCase();
        const webSearchCostPerCall = modelLower.startsWith("gpt-4")
          ? 25 / 1000 // $25 per 1k calls => $0.025
          : modelLower.includes("o3") || modelLower.includes("o4")
            ? 10 / 1000 // $10 per 1k calls => $0.01
            : 0;

        result.toolCalls.forEach((tc: any) => {
          if (tc.toolName === "code_interpreter") {
            extraToolDollars += 0.03; // per container spin-up
          }
          if (tc.toolName === "web_search_preview") {
            extraToolDollars += webSearchCostPerCall;
          }
        });
      }

      clearTimeout(timeoutId);

      // Calculate metrics
      const generationEndTime = Date.now();
      const generationTimeMs = generationEndTime - generationStartTime;

      const promptTokens =
        result.usage?.inputTokens ??
        Math.ceil(JSON.stringify(messagesWithSystemPrompt).length / 4);
      const completionTokens =
        result.usage?.outputTokens ?? Math.ceil((result.text || "").length / 4);
      const totalTokens =
        result.usage?.totalTokens ?? promptTokens + completionTokens;

      const tokensPerSecond =
        completionTokens > 0 && generationTimeMs > 0
          ? completionTokens / (generationTimeMs / 1000)
          : 0;

      const generationMetrics = {
        provider: actualProvider,
        model: actualModelId,
        tokensUsed: totalTokens,
        inputTokens: promptTokens,
        outputTokens: completionTokens,
        generationTimeMs,
        tokensPerSecond: Math.round(tokensPerSecond * 100) / 100,
        temperature,
        attemptedProviders: attemptedProviders.concat(actualProvider),
        fallbackUsed: attemptedProviders.length > 0,
        // Add more if needed, like reasoning time if available
      };

      // Only update usage if using built-in keys
      if (!usingUserKey) {
        // Deduct credits based on actual token usage
        const actualInputTokens = promptTokens;
        const actualOutputTokens = completionTokens;

        const shouldDeduct =
          actualInputTokens > 0 ||
          actualOutputTokens > 0 ||
          extraToolDollars > 0;

        if (shouldDeduct) {
          try {
            await ctx.runMutation(api.usage.deductCredits, {
              model: actualModelId,
              inputTokens: actualInputTokens,
              outputTokens: actualOutputTokens,
              extraDollars: extraToolDollars,
              provider: actualProvider,
            });
          } catch (creditError) {
            console.warn("Failed to deduct credits:", creditError);
            // Don't fail the whole request for credit deduction errors
          }
        }
      }

      // Save the assistant's response with proper tool calls format
      const toolCalls =
        result.toolCalls?.map((call, index) => {
          const toolResult = result.toolResults?.[index] as any;
          return {
            id: call.toolCallId || `tool_${Date.now()}_${index}`,
            name: call.toolName,
            arguments: JSON.stringify(call.input || {}),
            result: toolResult?.result
              ? JSON.stringify(toolResult.result)
              : undefined,
            startTime: generationStartTime,
            endTime: generationEndTime,
          };
        }) || undefined;

      // Ensure thinking doesn't contain duplicate content
      let finalContent =
        result.text?.trim() ||
        (typeof result.reasoningText === "string"
          ? result.reasoningText.trim()
          : "");

      let finalThinking =
        typeof result.reasoningText === "string"
          ? result.reasoningText.trim()
          : "";

      if (!finalContent && finalThinking) {
        finalContent = finalThinking;
        finalThinking = "";
      }

      await ctx.runMutation(api.messages.add, {
        conversationId: args.conversationId,
        branchId: branchId,
        role: "assistant",
        content:
          finalContent ||
          "I apologize, but I couldn't generate a response. The model may have returned empty content or encountered an issue during generation.",
        thinking: finalThinking || undefined,
        toolCalls,
        generationMetrics, // Add this
      });

      return {
        text: result.text,
        toolCalls: result.toolCalls,
        usingUserKey,
        generationMetrics,
      };
    } catch (error) {
      // Enhanced error logging for SDK v5 compatibility
      console.error(`[Provider Fallback] Error with ${actualProvider}:`, error);

      // Ensure we capture the error properly for SDK v5
      if (error instanceof Error) {
        lastError = error;
      } else if (typeof error === "object" && error !== null) {
        // Handle SDK v5 error objects that might not be Error instances
        const errorObj = error as any;
        lastError = new Error(
          errorObj.message || errorObj.error?.message || JSON.stringify(error)
        );
        // Preserve original error properties
        Object.assign(lastError, error);
      } else {
        lastError = new Error(String(error));
      }

      attemptedProviders.push(actualProvider);

      // Interface for extended error types that may include additional properties from AI SDK v5 libraries
      interface ExtendedErrorFallback extends Error {
        data?: {
          error?: {
            message?: string;
            type?: string;
            code?: string;
          };
        };
        responseBody?: string;
        code?: string;
        type?: string;
        status?: number;
        statusText?: string;
        // SDK v5 specific error properties
        cause?: any;
        details?: any;
        response?: {
          status?: number;
          statusText?: string;
          data?: any;
        };
      }

      // Helper function to extract error message from various nested locations (SDK v5 compatible)
      const getErrorMessageFallback = (error: any): string => {
        if (typeof error === "string") {
          return error;
        }
        if (error instanceof Error) {
          const extendedError = error as ExtendedErrorFallback;

          // Check multiple possible locations for error messages in SDK v5
          return (
            error.message ||
            extendedError.data?.error?.message ||
            extendedError.cause?.message ||
            extendedError.details?.message ||
            extendedError.response?.data?.error?.message ||
            extendedError.responseBody ||
            extendedError.statusText ||
            String(error)
          );
        }
        return String(error);
      };

      // Enhanced error type detection for SDK v5
      const getErrorTypeFallback = (error: any): string => {
        if (typeof error === "object" && error !== null) {
          const extendedError = error as ExtendedErrorFallback;
          return (
            extendedError.type ||
            extendedError.code ||
            extendedError.data?.error?.type ||
            extendedError.data?.error?.code ||
            "unknown"
          );
        }
        return "unknown";
      };

      // Enhanced logging for SDK v5 error details
      const fallbackErrorType = getErrorTypeFallback(error);
      const fallbackErrorMessage = getErrorMessageFallback(error);
      console.log(
        `[Provider Fallback] Error type: ${fallbackErrorType}, Message: ${fallbackErrorMessage.substring(0, 200)}...`
      );

      // Enhanced error categorization for SDK v5 with case-insensitive matching
      const errorMessageLower = fallbackErrorMessage.toLowerCase();
      const errorTypeLower = fallbackErrorType.toLowerCase();
      const extendedError = error as ExtendedErrorFallback;

      const isRetryableError =
        // Rate limiting errors
        errorMessageLower.includes("rate limit") ||
        errorMessageLower.includes("too many requests") ||
        errorTypeLower.includes("rate_limit") ||
        extendedError.status === 429 ||
        // Timeout errors
        errorMessageLower.includes("timeout") ||
        errorMessageLower.includes("timed out") ||
        errorTypeLower.includes("timeout") ||
        // Server errors
        errorMessageLower.includes("503") ||
        errorMessageLower.includes("502") ||
        errorMessageLower.includes("500") ||
        errorMessageLower.includes("internal server error") ||
        errorMessageLower.includes("service unavailable") ||
        errorMessageLower.includes("bad gateway") ||
        extendedError.status === 503 ||
        extendedError.status === 502 ||
        extendedError.status === 500 ||
        // Authentication errors (might work with different provider)
        errorMessageLower.includes("authentication") ||
        errorMessageLower.includes("api key") ||
        errorMessageLower.includes("invalid") ||
        errorMessageLower.includes("unauthorized") ||
        errorMessageLower.includes("forbidden") ||
        errorTypeLower.includes("auth") ||
        errorTypeLower.includes("invalid_api_key") ||
        extendedError.status === 401 ||
        extendedError.status === 403 ||
        // Network/connection errors
        errorMessageLower.includes("network") ||
        errorMessageLower.includes("connection") ||
        errorMessageLower.includes("fetch") ||
        errorMessageLower.includes("econnreset") ||
        errorMessageLower.includes("enotfound") ||
        // SDK v5 specific error types
        errorTypeLower.includes("network_error") ||
        errorTypeLower.includes("connection_error") ||
        errorTypeLower.includes("api_error") ||
        // Abort/cancellation errors
        errorMessageLower.includes("aborted") ||
        errorMessageLower.includes("cancelled") ||
        errorTypeLower.includes("abort");

      // Stop looping only when we have exhausted all providers. Always try the
      // next provider when one is still available, regardless of the specific
      // error type. This guarantees that we respect the provider priority
      // order and that failures trigger a proper fallback.
      if (providerIndex === validProvidersToTry.length - 1) {
        console.log(
          `[Provider Fallback] No more providers to try — all providers exhausted`
        );
        break;
      }

      console.log(
        `[Provider Fallback] Retryable error with ${actualProvider}, trying next provider`
      );

      // Clear timeout for this attempt
      if (abortController) {
        abortController.abort();
        abortController = null;
      }

      // Continue to next provider
      continue;
    }
  }

  // If we get here, all providers failed
  if (lastError) {
    // Clear timeout if it exists
    if (abortController) {
      abortController.abort();
    }

    let errorMessage: string;

    // Handle timeout errors specifically
    if (lastError instanceof Error && lastError.message.includes("timeout")) {
      const timeoutMinutes = actualModelId.includes("deep-research")
        ? 60
        : actualProvider === "google"
          ? 10
          : 5;
      errorMessage = `All providers failed. The last attempt with ${actualProvider} model (${actualModelId}) timed out after ${timeoutMinutes} minutes. ${actualModelId.includes("deep-research") ? "Deep research models require more time for complex reasoning." : "This can happen with complex requests or when the model is under heavy load."} Attempted providers: ${attemptedProviders.join(", ")}. Please try again or consider using a different model.`;
    } else if (
      lastError instanceof Error &&
      lastError.message.includes("aborted")
    ) {
      errorMessage = `Request was cancelled: ${lastError.message}`;
    } else {
      errorMessage = `All providers failed. ${getUserFriendlyErrorMessage(lastError, actualProvider, usingUserKey)} Attempted providers: ${attemptedProviders.join(", ")}.`;
    }

    console.error(
      `Generation error after trying all providers. Last error from ${actualProvider}/${actualModelId}:`,
      lastError
    );

    // Save error message as assistant response
    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content: errorMessage,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
      isError: true,
    });

    return {
      error: errorMessage,
      usingUserKey,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
    };
  } else {
    // This shouldn't happen, but handle it gracefully
    const errorMessage = "Unknown error occurred during generation";
    console.error("No error captured but all providers failed");

    // Save error message as assistant response
    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content: errorMessage,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
      isError: true,
    });

    return {
      error: errorMessage,
      usingUserKey,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
    };
  }
}
