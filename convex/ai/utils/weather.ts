"use node";

import { api } from "../../_generated/api";

// Helper function to get weather data
export async function getWeatherData(
  ctx: any,
  location: string,
  date?: string
): Promise<string> {
  try {
    const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
      provider: "openweather",
    });

    let apiKey = "";
    if (apiKeyRecord?.apiKey && apiKeyRecord.apiKey.trim().length > 0) {
      apiKey = apiKeyRecord.apiKey.trim();
    } else {
      apiKey = process.env.OPENWEATHER_API_KEY || "";
    }

    if (!apiKey) {
      if (date) {
        return `Forecast for ${location} on ${date}: This is simulated forecast data. Configure your OpenWeatherMap API key for real forecasts. Forecast: 19°C, Scattered clouds, Humidity: 70%, Wind: 12 km/h W.`;
      }
      return `Weather for ${location}: This is simulated weather data. Configure your OpenWeatherMap API key for real weather data. Current: 22°C, Partly cloudy, Humidity: 65%, Wind: 8 km/h NE.`;
    }

    const geoResponse = await fetch(
      `http://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(
        location
      )}&limit=1&appid=${apiKey}`
    );

    if (!geoResponse.ok) {
      throw new Error(`Geocoding API error: ${geoResponse.statusText}`);
    }

    const geoData = await geoResponse.json();
    if (!geoData.length) {
      return `Weather data not found for "${location}". Please check the location name.`;
    }

    const { lat, lon, name, country } = geoData[0];

    // Return a forecast if a date is provided
    if (date) {
      const forecastResponse = await fetch(
        `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
      );
      if (!forecastResponse.ok) {
        throw new Error(
          `Weather Forecast API error: ${forecastResponse.statusText}`
        );
      }
      const forecastData = await forecastResponse.json();

      const targetDate = new Date(date);
      targetDate.setHours(12, 0, 0, 0); // Normalize to noon

      const forecastForDay = forecastData.list.find((item: any) => {
        const itemDate = new Date(item.dt * 1000);
        return (
          itemDate.getFullYear() === targetDate.getFullYear() &&
          itemDate.getMonth() === targetDate.getMonth() &&
          itemDate.getDate() === targetDate.getDate()
        );
      });

      if (!forecastForDay) {
        return `No forecast available for ${location} on ${date}. Please provide a date within the next 5 days.`;
      }

      const weather = forecastForDay;
      return `Forecast for ${name}, ${country} on ${date}:
Temperature: ${Math.round(weather.main.temp)}°C (feels like ${Math.round(
        weather.main.feels_like
      )}°C)
Conditions: ${weather.weather[0].description}
Humidity: ${weather.main.humidity}%
Wind: ${Math.round(weather.wind.speed * 3.6)} km/h
Cloud cover: ${weather.clouds.all}%
`;
    }

    // Get current weather data if no date
    const weatherResponse = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${apiKey}&units=metric`
    );

    if (!weatherResponse.ok) {
      throw new Error(`Weather API error: ${weatherResponse.statusText}`);
    }

    const weather = await weatherResponse.json();

    return `Weather for ${name}, ${country}:
Current: ${Math.round(weather.main.temp)}°C, ${weather.weather[0].description}
Feels like: ${Math.round(weather.main.feels_like)}°C
Humidity: ${weather.main.humidity}%
Wind: ${Math.round(weather.wind.speed * 3.6)} km/h
Pressure: ${weather.main.pressure} hPa
Visibility: ${weather.visibility / 1000} km`;
  } catch (error) {
    return `Weather data unavailable for "${location}": ${
      error instanceof Error ? error.message : "Unknown error"
    }`;
  }
}
