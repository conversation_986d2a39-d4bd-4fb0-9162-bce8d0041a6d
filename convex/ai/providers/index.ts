import { openai } from "@ai-sdk/openai";
import { createAnthropic } from "@ai-sdk/anthropic";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { groq } from "@ai-sdk/groq";
import { deepseek } from "@ai-sdk/deepseek";
import { cohere as cohereProvider } from "@ai-sdk/cohere";
import { mistral as mistralProvider } from "@ai-sdk/mistral";
import { cerebras as cerebrasProvider } from "@ai-sdk/cerebras";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { wrapLanguageModel, extractReasoningMiddleware } from "ai";
import { cacheMiddleware } from "../middleware/cache";
import {
  PROVIDER_BASE_URLS,
  SUPPORTED_PROVIDERS,
  SupportedProvider,
} from "./constants";
import { PROVIDER_CONFIGS, getModelInfo } from "../../../src/lib/models";
// Provider options types are available but not exported, so we'll use any for now

// Helper to get the first model for a provider from the shared PROVIDER_CONFIGS.
// Falls back to a hard-coded default if the provider isn't found.
export const getDefaultModel = (provider: string): string => {
  const cfg = (PROVIDER_CONFIGS as Record<string, any>)[provider];
  if (cfg && Array.isArray(cfg.models) && cfg.models.length) {
    return cfg.models[0];
  }
  return "gemini-2.5-flash-preview-05-20"; // sensible global fallback
};

// Helper function to create AI model based on provider
export function createAIModel(args: {
  provider: string;
  model: string;
  apiKey: string;
  baseURL?: string;
  temperature?: number;
  maxOutputTokens?: number;
  thinkingBudget?: string | number;
  skipMiddleware?: boolean;
  customProvider?: {
    name: string;
    baseURL: string;
    models: string[];
  };
  enabledTools?: string[];
}) {
  let baseModel;
  const providerOptions: any = {};

  try {
    switch (args.provider) {
      case "openai": {
        // Use OpenAI Responses API for structured responses when possible
        // Note: responses API may not support custom baseURL/apiKey configuration
        // so we fall back to regular API if custom configuration is needed
        if (args.baseURL && args.baseURL !== "https://api.openai.com/v1") {
          // Custom baseURL provided, use regular API
          // Custom baseURL requires an OpenAI-compatible provider in v5
          const customOpenAI = createOpenAICompatible({
            name: "openai-custom",
            apiKey: args.apiKey,
            baseURL: args.baseURL || "https://api.openai.com/v1",
          });
          baseModel = customOpenAI(args.model as any);
        } else {
          // Use OpenAI Responses API for better structured responses
          // Set API key via environment variable for responses API
          const originalApiKey = process.env.OPENAI_API_KEY;
          process.env.OPENAI_API_KEY = args.apiKey;

          baseModel = openai.responses(args.model as any);

          // Restore original API key if it existed
          if (originalApiKey) {
            process.env.OPENAI_API_KEY = originalApiKey;
          } else {
            delete process.env.OPENAI_API_KEY;
          }
        }

        // Reasoning models - use reasoningEffort
        const modelInfo = getModelInfo(args.model);
        if (modelInfo.supportsThinking) {
          providerOptions.openai = {
            reasoningEffort: args.thinkingBudget || "medium",
            reasoningSummary: "auto",
          };
        }

        // Configure built-in OpenAI tools if they're available
        // This should be passed separately from the tools object
        const openaiOptions: any = {
          ...providerOptions.openai,
        };

        // Add built-in tools configuration
        const tools: any[] = [];

        // Deep-research models REQUIRE at least one of the native
        // `web_search_preview` **or** `mcp` tools. Prefer to include BOTH the
        // built-in web search preview and the code interpreter helpers to give
        // the agent maximum capabilities. Use the official OpenAI SDK builder
        // functions so the request payload exactly matches OpenAI’s
        // expectations. Fallback to a minimal stub if the helper isn’t
        // available (e.g. for older SDK versions) so we never crash.

        const addBuiltInTool = (
          name: "web_search_preview" | "code_interpreter"
        ) => {
          try {
            // The OpenAI SDK exposes strongly-typed helpers under
            // `openai.tools`. They guarantee the correct JSON schema.
            const builder = (openai as any).tools?.[
              name === "web_search_preview"
                ? "webSearchPreview"
                : "codeInterpreter"
            ];
            if (typeof builder === "function") {
              // `codeInterpreter` takes an optional config – we pass an empty
              // object to keep it simple whilst allowing the SDK to fill in
              // sensible defaults (container: {type: "auto"}, etc.).
              tools.push(
                name === "web_search_preview" ? builder() : builder({})
              );
              return;
            }
          } catch (err) {
            console.warn(`OpenAI SDK builder for '${name}' failed:`, err);
          }

          // Fallback: push the minimal object expected by the API so we still
          // satisfy the model pre-flight checks even if the helper is missing.
          if (name === "web_search_preview") {
            tools.push({ type: "web_search_preview" });
          } else {
            tools.push({
              type: "code_interpreter",
              container: { type: "auto" },
            });
          }
        };

        // 1. Automatically add both tools for the dedicated deep-research models.
        if (args.model === "o4-mini-deep-research") {
          addBuiltInTool("web_search_preview");
          addBuiltInTool("code_interpreter");
        }

        // 2. Respect the explicit tool settings coming from the caller.
        if (args.enabledTools) {
          if (
            args.enabledTools.includes("web_search_preview") &&
            !tools.some(
              (t: any) =>
                (typeof t.id === "string" &&
                  t.id === "openai.web_search_preview") ||
                t.type === "web_search_preview"
            )
          ) {
            addBuiltInTool("web_search_preview");
          }

          if (
            args.enabledTools.includes("code_interpreter") &&
            !tools.some(
              (t: any) =>
                (typeof t.id === "string" &&
                  t.id === "openai.code_interpreter") ||
                t.type === "code_interpreter"
            )
          ) {
            addBuiltInTool("code_interpreter");
          }
        }

        if (tools.length > 0) {
          openaiOptions.tools = tools;
        }

        providerOptions.openai = openaiOptions;

        break;
      }

      case "anthropic": {
        if (args.baseURL) {
          // Use createAnthropic for custom configuration
          const customAnthropic = createAnthropic({
            apiKey: args.apiKey,
            baseURL: args.baseURL,
          });
          baseModel = customAnthropic(args.model as any);
        } else {
          // Use default anthropic provider with custom apiKey
          const defaultAnthropic = createAnthropic({
            apiKey: args.apiKey,
          });
          baseModel = defaultAnthropic(args.model as any);
        }

        // Native thinking support for Anthropic models
        const anthropicModelInfo = getModelInfo(args.model);
        if (anthropicModelInfo.supportsThinking) {
          providerOptions.anthropic = {
            thinking: {
              type: "enabled",
              budgetTokens:
                typeof args.thinkingBudget === "number"
                  ? args.thinkingBudget
                  : 15000,
            },
          };
        }
        break;
      }

      case "google": {
        if (args.baseURL) {
          // Use createGoogleGenerativeAI for custom configuration
          const customGoogle = createGoogleGenerativeAI({
            apiKey: args.apiKey,
            baseURL: args.baseURL,
          });
          baseModel = customGoogle(args.model as any);
        } else {
          // Use default google provider with custom apiKey
          const defaultGoogle = createGoogleGenerativeAI({
            apiKey: args.apiKey,
          });
          baseModel = defaultGoogle(args.model as any);
        }

        // Native thinking support for Google/Gemini models
        const googleModelInfo = getModelInfo(args.model);
        if (googleModelInfo.supportsThinking) {
          const thinkingBudget =
            typeof args.thinkingBudget === "number"
              ? args.thinkingBudget
              : 2048;

          providerOptions.google = {
            thinkingConfig: {
              includeThoughts: true,
              thinkingBudget,
            },
          };
        }
        break;
      }

      case "groq":
        baseModel = groq(args.model as any);
        break;

      case "openrouter": {
        // Use OpenAI-compatible wrapper for OpenRouter to avoid strict content structure requirements
        const openrouterCompatible = createOpenAICompatible({
          name: "openrouter",
          apiKey: args.apiKey,
          baseURL: args.baseURL || "https://openrouter.ai/api/v1",
        });
        baseModel = openrouterCompatible(args.model as any);
        break;
      }

      case "deepseek":
        baseModel = deepseek(args.model as any);

        // Configure reasoning for DeepSeek R1 models
        const modelInfo = getModelInfo(args.model);
        if (modelInfo.supportsThinking) {
          // DeepSeek reasoning models don't use provider options like OpenAI
          // The reasoning is handled automatically by the model
          console.log(`[DeepSeek] Configured reasoning model: ${args.model}`);
        }
        break;

      case "grok": {
        const grokProvider = createOpenAICompatible({
          name: "grok",
          apiKey: args.apiKey,
          baseURL: args.baseURL || "https://api.x.ai/v1",
        });
        baseModel = grokProvider(args.model as any);
        break;
      }

      case "cohere":
        baseModel = cohereProvider(args.model as any);
        break;

      case "mistral":
        baseModel = mistralProvider(args.model as any);
        break;

      case "cerebras":
        baseModel = cerebrasProvider(args.model as any);
        break;

      case "github": {
        const githubProvider = createOpenAICompatible({
          name: "github",
          apiKey: args.apiKey,
          baseURL: args.baseURL || "https://models.inference.ai.azure.com",
        });
        baseModel = githubProvider(args.model as any);
        break;
      }

      default:
        // Check if this is a custom provider
        if (args.customProvider) {
          const customProviderInstance = createOpenAICompatible({
            name: args.customProvider.name,
            apiKey: args.apiKey,
            baseURL: args.customProvider.baseURL,
          });
          baseModel = customProviderInstance(args.model as any);
        } else {
          throw new Error(`Unsupported provider: ${args.provider}`);
        }
    }

    // Apply reasoning middleware for providers that need it
    // Some providers have native reasoning support, others need middleware to extract <think> tags
    const modelInfo = getModelInfo(args.model);
    const hasNativeReasoning =
      (["openai", "anthropic", "google"].includes(args.provider) ||
        (args.provider === "deepseek" && args.model === "deepseek-reasoner")) &&
      modelInfo.supportsThinking;
    const needsMiddleware = !hasNativeReasoning && modelInfo.supportsThinking;

    console.log(
      `[Middleware Debug] Model: ${args.model}, Provider: ${args.provider}`
    );
    console.log(
      `[Middleware Debug] Has native reasoning: ${hasNativeReasoning}`
    );
    console.log(`[Middleware Debug] Needs middleware: ${needsMiddleware}`);

    let finalModel;
    if (needsMiddleware && !args.skipMiddleware) {
      // Apply reasoning middleware for providers that need it
      finalModel = wrapLanguageModel({
        model: baseModel as any,
        middleware: extractReasoningMiddleware({
          tagName: getReasoningTagName(args.provider, args.model),
        }),
      });
    } else {
      finalModel = baseModel;
    }

    // Apply caching middleware to all models to reduce redundant provider calls.
    finalModel = wrapLanguageModel({
      model: finalModel as any,
      middleware: cacheMiddleware,
    });

    return {
      model: finalModel,
      providerOptions,
      hasNativeThinking: !needsMiddleware,
    };
  } catch (error) {
    console.error(
      `[AI Provider] Error creating model ${args.provider}/${args.model}:`,
      error
    );
    throw new Error(
      `Failed to create model: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
}

function getReasoningTagName(provider: string, model: string): string {
  // Tag patterns for different providers/models
  if (provider === "deepseek" || model.toLowerCase().includes("deepseek")) {
    return "think";
  }
  if (provider === "groq" && model.toLowerCase().includes("qwq")) {
    return "think";
  }
  if (model.toLowerCase().includes("r1")) {
    return "think";
  }

  // Default reasoning tags
  return "thinking";
}

// Helper function to get API key for a provider based on user preferences
export function getProviderApiKey(
  provider: SupportedProvider,
  apiKeyRecord?: { apiKey: string } | null
): { apiKey: string; usingUserKey: boolean } {
  // Map provider -> canonical ENV variable name used by most SDKs
  const ENV_VAR_MAP: Record<string, string> = {
    openai: "OPENAI_API_KEY",
    google: "GOOGLE_GENERATIVE_AI_API_KEY",
    anthropic: "ANTHROPIC_API_KEY",
    openrouter: "OPENROUTER_API_KEY",
    groq: "GROQ_API_KEY",
    deepseek: "DEEPSEEK_API_KEY",
    grok: "GROK_API_KEY",
    cohere: "COHERE_API_KEY",
    mistral: "MISTRAL_API_KEY",
    cerebras: "CEREBRAS_API_KEY",
    github: "GITHUB_TOKEN",
    exa: "EXA_API_KEY",
    tavily: "TAVILY_API_KEY",
    openweather: "OPENWEATHER_API_KEY",
    firecrawl: "FIRECRAWL_API_KEY",
  } as const;

  let apiKey = "";
  let usingUserKey = false;

  // PRIORITIZE USER'S API KEY FIRST
  if (
    apiKeyRecord &&
    typeof apiKeyRecord.apiKey === "string" &&
    apiKeyRecord.apiKey.trim().length > 0
  ) {
    apiKey = apiKeyRecord.apiKey.trim();
    usingUserKey = true;
  } else {
    // Use built-in keys only as fallback
    if (provider === "openai") {
      apiKey =
        process.env.CONVEX_OPENAI_API_KEY ?? process.env.OPENAI_API_KEY ?? "";
    } else if (provider === "google") {
      apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY ?? "";
    } else if (provider === "anthropic") {
      apiKey = process.env.ANTHROPIC_API_KEY ?? "";
    } else if (provider === "openrouter") {
      apiKey = process.env.OPENROUTER_API_KEY ?? "";
    } else if (provider === "groq") {
      apiKey = process.env.GROQ_API_KEY ?? "";
    } else if (provider === "deepseek") {
      apiKey = process.env.DEEPSEEK_API_KEY ?? "";
    } else if (provider === "grok") {
      apiKey = process.env.GROK_API_KEY ?? "";
    } else if (provider === "cohere") {
      apiKey = process.env.COHERE_API_KEY ?? "";
    } else if (provider === "mistral") {
      apiKey = process.env.MISTRAL_API_KEY ?? "";
    } else if (provider === "cerebras") {
      apiKey = process.env.CEREBRAS_API_KEY ?? "";
    } else if (provider === "github") {
      apiKey = process.env.GITHUB_TOKEN ?? "";
    } else if (provider === "exa") {
      apiKey = process.env.EXA_API_KEY ?? "";
    } else if (provider === "tavily") {
      apiKey = process.env.TAVILY_API_KEY ?? "";
    } else if (provider === "openweather") {
      apiKey = process.env.OPENWEATHER_API_KEY ?? "";
    } else if (provider === "firecrawl") {
      apiKey = process.env.FIRECRAWL_API_KEY ?? "";
    }
  }

  // Ensure downstream SDKs that rely on ENV variables still receive the key.
  // We only mutate the env when we have a (non-empty) key – this is safe in
  // Node runtimes and avoids leaking keys when they are intentionally empty.
  if (apiKey) {
    const envName = ENV_VAR_MAP[provider];
    if (envName && (!process.env[envName] || process.env[envName] !== apiKey)) {
      process.env[envName] = apiKey;
    }
  }

  const result = { apiKey, usingUserKey };

  return result;
}

// Export constants and types
export { PROVIDER_BASE_URLS, SUPPORTED_PROVIDERS, getModelInfo };
export type { SupportedProvider };
