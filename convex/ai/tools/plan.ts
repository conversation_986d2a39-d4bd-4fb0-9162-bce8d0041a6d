"use node";

import { tool } from "ai";
import { jsonSchema } from "ai";
import { api } from "../../_generated/api";
import { getProviderApi<PERSON>ey, createAIModel } from "../providers";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { generateText } from "ai";

export function createPlanTool(ctx: any, enabledTools: string[] = []) {
  return tool({
    description: `Quick task planner for AI tool coordination. Use this when you need to coordinate 3+ tools to complete a request efficiently.

**When to use:**
- Need 3+ different tools for a task
- Multi-step workflows requiring coordination
- Want to plan optimal tool sequence before execution

**Creates concise plans with:**
- Tool execution order
- Parallel vs sequential steps
- Key data flow between tools
- Simple backup approaches

Generates brief, actionable plans for immediate AI execution.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        request: {
          type: "string",
          description:
            "The complex user request that requires orchestration across multiple tools and capabilities",
        },
        constraints: {
          type: "object",
          properties: {
            time_limit: {
              type: "string",
              description:
                "Time constraints (e.g., 'urgent', '5 minutes', 'by end of day')",
            },
            priority_order: {
              type: "array",
              items: { type: "string" },
              description:
                "Ordered list of what's most important (e.g., ['accuracy', 'speed', 'completeness'])",
            },
            excluded_tools: {
              type: "array",
              items: { type: "string" },
              description: "Tools to avoid using",
            },
            required_outputs: {
              type: "array",
              items: { type: "string" },
              description: "Specific deliverables that must be produced",
            },
          },
          required: [
            "time_limit",
            "priority_order",
            "excluded_tools",
            "required_outputs",
          ],
          additionalProperties: false,
          description:
            "Constraints and requirements for the planning process. Leave properties empty if no specific constraints.",
        },
        complexity_level: {
          type: "string",
          enum: ["standard", "high", "enterprise"],
          description:
            "Expected complexity level for planning depth. Use 'standard' for default complexity.",
        },
      },
      required: ["request", "constraints", "complexity_level"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { request, constraints = {}, complexity_level = "standard" } = args;

      if (!request || request.trim() === "") {
        return `# ❌ Planning Error

**Issue:** No request provided for orchestration planning.

**I need a complex request that requires coordination across multiple tools and capabilities.**

**Examples of requests I can orchestrate:**
- "Research AI trends, analyze the data, create a comprehensive report with visualizations, and send it to stakeholders"
- "Find all TODO items in the codebase, analyze their priority, calculate development time estimates, and create a project plan"
- "Gather weather data for multiple cities, analyze patterns, generate comparison charts, and create a travel recommendation report"
- "Research competitors, analyze their pricing, calculate our competitive position, and create a strategic presentation"
- "Scan recent emails for action items, categorize them by urgency, estimate completion times, and generate a priority task list"`;
      }

      const complexity = complexity_level || "standard";
      const userConstraints = constraints || {};

      // Choose fast model for planning
      const provider = "groq";
      const model = "llama-3.1-8b-instant";

      // Get API key
      const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider,
      });
      const { apiKey } = getProviderApiKey(provider, apiKeyRecord);

      if (!apiKey) {
        return "Error: No Groq API key available for planning tool.";
      }

      // Create AI model
      const { model: aiModel } = createAIModel({
        provider,
        model,
        apiKey,
        baseURL: PROVIDER_BASE_URLS[provider],
      });

      // Create refined prompt for planning
      const planningPrompt = `Create a brief execution plan for this request using available tools.

Request: ${request}
Available Tools: ${enabledTools.length > 0 ? enabledTools.join(", ") : "No tools enabled"}

Output format:
## Plan
1. Tool: [tool_name] - [what it does]
2. Tool: [tool_name] - [what it does] 
3. Tool: [tool_name] - [what it does]

## Execution Notes
- Parallel: [tools that can run together]
- Sequential: [tools that must run in order]
- Data flow: [key info passed between tools]

Keep it brief - max 150 words total. Focus only on tool coordination.`;

      const { text } = await generateText({
        model: aiModel,
        prompt: planningPrompt,
        temperature: 0.3,
        maxOutputTokens: 300,
      });

      return text || "No plan generated.";
    },
  });
}
