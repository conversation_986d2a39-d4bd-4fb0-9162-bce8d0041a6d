"use node";

import { getModelInfo } from "../../../src/lib/models";
import {
  createWebSearchTool,
  createDeepSearchTool,
  createResearchTool,
} from "./webSearch";
import { createWeatherTool } from "./weather";
import { createDateTimeTool } from "./datetime";
import { createCalculatorTool } from "./calculator";
import { createThinkingTool } from "./thinking";
import { createPlanTool } from "./plan";
import { createMemoryTool } from "./memory";
import { createUrlFetchTool } from "./urlFetch";
import { createCodeAnalysisTool } from "./codeAnalysis";
import { createImageGenerationTool } from "./imageGeneration";
import { createMCPTools, getMCPToolInfo } from "./mcp";
import { canvasTool } from "./canvas";
import { createN8nTools, getN8nToolInfo } from "./n8n";
import {
  createN8nGenericTools,
  getN8nGenericToolInfo,
  createN8nServerTools,
  getN8nServerToolInfo,
} from "./n8n_tools";
import { Doc } from "../../_generated/dataModel";
import { createContextTool } from "./context";
import { tool, jsonSchema } from "ai";

// Export all tool creators
export {
  createWebSearchTool,
  createDeepSearchTool,
  createResearchTool,
  createWeatherTool,
  createDateTimeTool,
  createCalculatorTool,
  createThinkingTool,
  createPlanTool,
  createMemoryTool,
  createUrlFetchTool,
  createCodeAnalysisTool,
  createImageGenerationTool,
  createMCPTools,
  getMCPToolInfo,
  canvasTool,
  createN8nTools,
  getN8nToolInfo,
};

// ---------------------------------------------------------------------------
// Internal helper – only add tool if creator returns a valid object with a
// `parameters` key (the AI-SDK `tool()` helper always does). This prevents
// accidental insertion of empty strings or undefined values that break the
// OpenAI JSON-schema validation.
// ---------------------------------------------------------------------------

// Universal error handling wrapper for tool execution
function wrapToolWithErrorHandling(toolObj: any, toolId: string): any {
  if (!toolObj || typeof toolObj !== "object" || !toolObj.execute) {
    return toolObj;
  }

  const originalExecute = toolObj.execute;

  return {
    ...toolObj,
    execute: async (...args: any[]) => {
      try {
        const result = await originalExecute(...args);
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        console.error(`Tool '${toolId}' execution failed:`, error);

        // Return a user-friendly error message instead of crashing
        return `❌ **Tool Error: ${toolId.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}**

The tool encountered an error and couldn't complete the request.

**Error details:** ${errorMessage}

**What you can try:**
- Retry the request with different parameters
- Check if the tool requires specific API keys in Settings
- Try a different approach or tool for this task

I'll continue with the conversation despite this tool failure.`;
      }
    },
  };
}

// Helper function to wrap all tools in an object with error handling
function wrapAllToolsWithErrorHandling(
  tools: Record<string, any>
): Record<string, any> {
  const wrappedTools: Record<string, any> = {};

  for (const [toolId, tool] of Object.entries(tools)) {
    wrappedTools[toolId] = wrapToolWithErrorHandling(tool, toolId);
  }

  return wrappedTools;
}

function addTool(map: Record<string, any>, id: string, creator: () => any) {
  try {
    const t = creator();
    // Check for v5 inputSchema (new) or v4 parameters (legacy)
    if (
      t &&
      typeof t === "object" &&
      ("inputSchema" in t || "parameters" in t)
    ) {
      // Wrap the tool with error handling to prevent crashes
      map[id] = wrapToolWithErrorHandling(t, id);
      console.log(`[Tool Registered] ${id} successfully added`);
    } else {
      console.warn(
        `[Tool Rejected] ${id} - missing inputSchema/parameters:`,
        Object.keys(t || {})
      );
    }
  } catch (err) {
    console.warn(`Tool '${id}' failed to initialise:`, err);

    // Create a fallback tool that explains the initialization failure
    map[id] = {
      description: `Tool ${id} failed to initialize`,
      inputSchema: {
        type: "object",
        properties: {},
        required: [],
        additionalProperties: false,
      },
      execute: async () => {
        return `❌ **Tool Initialization Error: ${id.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}**

This tool failed to initialize properly.

**What you can try:**
- Check if required API keys are configured in Settings
- Verify your internet connection
- Try using a different tool for this task

I'll continue with the conversation despite this tool being unavailable.`;
      },
    };
  }
}

// Helper function to create tools based on enabled tools and model capabilities
export async function createAvailableTools(
  ctx: any,
  enabledTools: string[],
  model: string,
  usingUserKey: boolean,
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
): Promise<Record<string, any>> {
  // Check if the selected model supports tools
  const modelInfo = getModelInfo(model);
  const modelSupportsTools = modelInfo.supportsTools;

  // Create tools based on enabled tools (only if model supports them)
  const availableTools: Record<string, any> = {};

  // If model doesn't support tools but user has tools enabled, return empty tools
  if (!modelSupportsTools && enabledTools.length > 0) {
    // Log specific information about why tools are disabled
    console.warn(
      `⚠️ Tools disabled: Model "${model}" (${modelInfo.displayName}) doesn't support function calling.`
    );

    // Special message for Groq compound models
    if (model.includes("compound")) {
      console.warn(
        `💡 Groq Compound models use agentic tooling instead of function calling. Consider using models like:`,
        `- llama-3.3-70b-versatile`,
        `- llama-3.1-8b-instant`,
        `- qwen-qwq-32b`,
        `- deepseek-r1-distill-llama-70b`
      );
    }

    return availableTools; // Return empty tools object
  }

  // Only create tools if model supports them
  if (!modelSupportsTools) {
    return availableTools;
  }

  if (enabledTools.includes("web_search"))
    addTool(availableTools, "web_search", () =>
      createWebSearchTool(ctx, usingUserKey)
    );

  if (enabledTools.includes("research"))
    addTool(availableTools, "research", () =>
      createResearchTool(ctx, usingUserKey)
    );

  if (enabledTools.includes("deep_search"))
    addTool(availableTools, "deep_search", () =>
      createDeepSearchTool(ctx, usingUserKey)
    );

  if (enabledTools.includes("weather"))
    addTool(availableTools, "weather", () => createWeatherTool(ctx));

  if (enabledTools.includes("datetime"))
    addTool(availableTools, "datetime", () => createDateTimeTool());

  if (enabledTools.includes("calculator"))
    addTool(availableTools, "calculator", () => createCalculatorTool());

  if (enabledTools.includes("thinking"))
    addTool(availableTools, "thinking", () =>
      createThinkingTool(ctx, enabledTools)
    );

  if (enabledTools.includes("plan"))
    addTool(availableTools, "plan", () => createPlanTool(ctx, enabledTools));

  if (enabledTools.includes("memory"))
    addTool(availableTools, "memory", () => createMemoryTool(ctx));

  if (enabledTools.includes("url_fetch"))
    addTool(availableTools, "url_fetch", () => createUrlFetchTool(ctx));

  if (enabledTools.includes("code_analysis"))
    addTool(availableTools, "code_analysis", () => createCodeAnalysisTool());

  if (enabledTools.includes("image_generation"))
    addTool(availableTools, "image_generation", () =>
      createImageGenerationTool(ctx)
    );

  if (enabledTools.includes("canvas"))
    addTool(availableTools, "canvas", () => canvasTool);

  if (enabledTools.includes("conversation_context"))
    addTool(availableTools, "conversation_context", () =>
      createContextTool(ctx)
    );

  // Add dummies for OpenAI built-in tools in deep research models
  if (model.includes("deep-research")) {
    if (enabledTools.includes("web_search_preview")) {
      availableTools["web_search_preview"] = tool({
        description: "OpenAI built-in web search preview tool",
        inputSchema: jsonSchema({
          type: "object",
          properties: {},
          required: [],
          additionalProperties: false,
        }),
        execute: async () => ({ result: "Built-in tool executed" }),
      });
    }
    if (enabledTools.includes("code_interpreter")) {
      availableTools["code_interpreter"] = tool({
        description: "OpenAI built-in code interpreter tool",
        inputSchema: jsonSchema({
          type: "object",
          properties: {},
          required: [],
          additionalProperties: false,
        }),
        execute: async () => ({ result: "Built-in tool executed" }),
      });
    }
  }

  // NOTE: OpenAI's built-in `web_search_preview` and `code_interpreter` tools
  // are now included via the OpenAI provider builder helpers inside
  // `convex/ai/providers/index.ts`. We intentionally do NOT add any placeholder
  // objects here to avoid sending duplicate or malformed definitions.

  // Add MCP tools if servers are provided
  const enabledMcpServers = mcpServers.filter(
    (server) =>
      server.isEnabled &&
      enabledTools.includes(
        `mcp_${server.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}`
      )
  );

  if (enabledMcpServers.length > 0) {
    try {
      const mcpTools = await createMCPTools(enabledMcpServers);
      Object.assign(availableTools, mcpTools);
    } catch (error) {
      console.error("Failed to create MCP tools:", error);
    }
  }

  // Add n8n server-bound tools if any servers are enabled
  const enabledN8nServers = n8nServers.filter(
    (server) =>
      server.isEnabled &&
      enabledTools.includes(
        `n8n_${server.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}`
      )
  );

  if (enabledN8nServers.length > 0) {
    try {
      const serverTools = createN8nServerTools(enabledN8nServers);
      Object.assign(availableTools, serverTools);
    } catch (err) {
      console.error("Failed to create bound n8n server tools:", err);
    }
  }

  // Add n8n workflow tools if provided (legacy support)
  const enabledWorkflows = n8nWorkflows.filter((workflow) => {
    if (!workflow.isEnabled) return false;
    const rawName =
      workflow.name && workflow.name.trim()
        ? workflow.name.trim()
        : `workflow_${workflow._id.toString().slice(-6)}`;
    let sanitized = rawName.toLowerCase().replace(/[^a-z0-9]/g, "_");
    if (sanitized === "")
      sanitized = `workflow_${workflow._id.toString().slice(-6)}`;
    return enabledTools.includes(`n8n_${sanitized}`);
  });

  if (enabledWorkflows.length > 0) {
    try {
      const n8nTools = createN8nTools(enabledWorkflows);
      Object.assign(availableTools, n8nTools);
    } catch (error) {
      console.error("Failed to create n8n workflow tools:", error);
    }
  }

  // Expose generic n8n tools (parameterised) only if there are NO enabled servers
  if (!n8nServers || n8nServers.length === 0) {
    try {
      const genericN8nTools = createN8nGenericTools();
      Object.entries(genericN8nTools).forEach(([key, value]) => {
        if (enabledTools.includes(key)) {
          availableTools[key] = value;
        }
      });
    } catch (error) {
      console.error("Failed to create generic n8n tools:", error);
    }
  }

  // Apply error handling wrapper to ALL tools before returning
  return wrapAllToolsWithErrorHandling(availableTools);
}

// Tool configuration for UI
export const TOOL_CONFIGS = {
  web_search: {
    id: "web_search",
    name: "Web Search",
    description: "Search the web for current information",
    requiresApiKey: "tavily",
    category: "search",
  },
  deep_search: {
    id: "deep_search",
    name: "Deep Search",
    description: "Comprehensive research with multiple queries",
    requiresApiKey: "exa",
    category: "search",
    premium: false,
  },
  research: {
    id: "research",
    name: "Research",
    description: "Autonomous deep research via Exa.ai",
    requiresApiKey: "exa",
    category: "search",
    premium: false,
  },
  weather: {
    id: "weather",
    name: "Weather",
    description: "Get current weather information",
    requiresApiKey: "openweather",
    category: "information",
  },
  datetime: {
    id: "datetime",
    name: "Date & Time",
    description: "Get current date and time information",
    requiresApiKey: null,
    category: "information",
  },
  calculator: {
    id: "calculator",
    name: "Calculator",
    description: "Perform mathematical calculations",
    requiresApiKey: null,
    category: "computation",
  },
  thinking: {
    id: "thinking",
    name: "Thinking",
    description: "Think through complex problems step by step",
    requiresApiKey: null,
    category: "reasoning",
  },
  plan: {
    id: "plan",
    name: "Plan",
    description: "Break down complex requests into actionable plans",
    requiresApiKey: null,
    category: "planning",
  },
  memory: {
    id: "memory",
    name: "Memory",
    description: "Store and retrieve conversation memory",
    requiresApiKey: null,
    category: "utility",
  },
  url_fetch: {
    id: "url_fetch",
    name: "URL Fetch",
    description: "Fetch content from URLs to analyze or summarize",
    requiresApiKey: null,
    category: "utility",
  },
  code_analysis: {
    id: "code_analysis",
    name: "Code Analysis",
    description: "Analyze code for issues, improvements, or explanations",
    requiresApiKey: null,
    category: "development",
  },
  image_generation: {
    id: "image_generation",
    name: "Image Generation",
    description: "Generate images from text descriptions using AI",
    requiresApiKey: "cloudflare",
    category: "creative",
  },
  canvas: {
    id: "canvas",
    name: "Canvas",
    description:
      "Create interactive markdown documents or code projects with live preview",
    requiresApiKey: null,
    category: "creative",
  },
  conversation_context: {
    id: "conversation_context",
    name: "Conversation Context",
    description:
      "Semantic search across all of the user's conversations (optionally filter to one)",
    requiresApiKey: null,
    category: "search",
  },
} as const;

export type ToolId = keyof typeof TOOL_CONFIGS;

// Function to get available tools configuration
export function getAvailableToolsConfig(
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
): typeof TOOL_CONFIGS & Record<string, any> {
  const baseConfig = { ...TOOL_CONFIGS };

  // Add MCP server tool configs
  if (mcpServers && mcpServers.length > 0) {
    const mcpConfigs = getMCPToolInfo(mcpServers);
    mcpConfigs.forEach((config) => {
      (baseConfig as any)[config.id] = config;
    });
  }

  // Add n8n server tool configs (one per enabled server)
  if (n8nServers && n8nServers.length > 0) {
    const serverInfos = getN8nServerToolInfo(n8nServers);
    serverInfos.forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  // Legacy: add n8n workflow tool configs
  if (n8nWorkflows && n8nWorkflows.length > 0) {
    const n8nInfos = getN8nToolInfo(n8nWorkflows);
    n8nInfos.forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  // Add generic n8n tool configs ONLY if no servers configured
  if (!n8nServers || n8nServers.length === 0) {
    getN8nGenericToolInfo().forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  return baseConfig as typeof TOOL_CONFIGS & Record<string, any>;
}

// Create tools based on enabled configuration
export async function createTools(
  ctx: any,
  enabledTools: string[] = [],
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
) {
  const tools: Record<string, any> = {};

  // Add base tools as needed
  if (enabledTools.includes("web_search"))
    addTool(tools, "web_search", () => createWebSearchTool(ctx, false));

  if (enabledTools.includes("calculator"))
    addTool(tools, "calculator", () => createCalculatorTool());

  if (enabledTools.includes("datetime"))
    addTool(tools, "datetime", () => createDateTimeTool());

  if (enabledTools.includes("weather"))
    addTool(tools, "weather", () => createWeatherTool(ctx));

  if (enabledTools.includes("memory"))
    addTool(tools, "memory", () => createMemoryTool(ctx));

  if (enabledTools.includes("thinking"))
    addTool(tools, "thinking", () => createThinkingTool(ctx, enabledTools));

  if (enabledTools.includes("plan"))
    addTool(tools, "plan", () => createPlanTool(ctx, enabledTools));

  if (enabledTools.includes("canvas"))
    addTool(tools, "canvas", () => canvasTool);

  if (enabledTools.includes("url_fetch"))
    addTool(tools, "url_fetch", () => createUrlFetchTool(ctx));

  if (enabledTools.includes("image_generation"))
    addTool(tools, "image_generation", () => createImageGenerationTool(ctx));

  if (enabledTools.includes("code_analysis"))
    addTool(tools, "code_analysis", () => createCodeAnalysisTool());

  // Add MCP tools if any are enabled
  const mcpToolIds = mcpServers
    .filter((s) => s.isEnabled)
    .map((s) => `mcp_${s.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}`);

  if (mcpToolIds.some((id) => enabledTools.includes(id))) {
    try {
      const mcpTools = await createMCPTools(
        mcpServers.filter((s) => s.isEnabled)
      );

      // Only add enabled MCP tools
      Object.entries(mcpTools).forEach(([key, value]) => {
        const toolBaseId = key.split("_").slice(0, 2).join("_");
        if (enabledTools.includes(toolBaseId)) {
          tools[key] = value;
        }
      });
    } catch (error) {
      console.error("Failed to create MCP tools:", error);
    }
  }

  // Add server-bound n8n tools if provided
  if (n8nServers && n8nServers.length > 0) {
    try {
      const serverTools = createN8nServerTools(n8nServers);
      Object.entries(serverTools).forEach(([key, value]) => {
        const baseId = key.split("_", 3).slice(0, 2).join("_"); // e.g., n8n_myserver
        if (enabledTools.includes(baseId)) {
          tools[key] = value;
        }
      });
    } catch (err) {
      console.error("Failed to create n8n server tools:", err);
    }
  }

  // Legacy workflow tools
  if (n8nWorkflows && n8nWorkflows.length > 0) {
    try {
      const n8nTools = createN8nTools(n8nWorkflows);
      Object.entries(n8nTools).forEach(([key, value]) => {
        if (enabledTools.includes(key)) {
          tools[key] = value;
        }
      });
    } catch (error) {
      console.error("Failed to create n8n workflow tools:", error);
    }
  }

  if (enabledTools.includes("conversation_context")) {
    tools.conversation_context = createContextTool(ctx);
  }

  // Apply error handling wrapper to ALL tools before returning
  return wrapAllToolsWithErrorHandling(tools);
}
