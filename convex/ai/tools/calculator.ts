"use node";

import { tool } from "ai";
import { z } from "zod";
import { jsonSchema } from "ai";
import { evaluate } from "mathjs";

export function createCalculatorTool() {
  return tool({
    description:
      "Perform complex mathematical calculations and computations. Use ONLY for mathematical problems that require precise calculation, complex formulas, or multi-step arithmetic. Do NOT use for simple math you can do directly (like 2+2, basic percentages, or obvious calculations). Reserved for genuinely complex computational tasks.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        expression: {
          type: "string",
          description:
            "The complex mathematical expression or calculation that requires computational assistance. Supports basic arithmetic (+, -, *, /), parentheses, exponents (^), functions like sin, cos, tan, log, sqrt, abs, etc.",
        },
      },
      required: ["expression"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { expression } = args;

      // Handle empty expression
      if (!expression || expression.trim() === "") {
        return `Error: No mathematical expression provided. Please provide a calculation to perform.

Examples:
- sqrt(144) + 15 * 0.15
- (100 + 200) * 1.08
- sin(pi/2) + cos(0)
- log(100) / log(10)
- 2^8 - 3^4`;
      }

      try {
        // Clean the expression
        const cleanExpression = expression.trim();

        // Basic security checks
        if (cleanExpression.length > 200) {
          return `Error: Expression too long. Please use expressions under 200 characters.`;
        }

        // Check for potentially dangerous patterns
        const dangerousPatterns = [
          /\bimport\b/i,
          /\brequire\b/i,
          /\beval\b/i,
          /\bfunction\b/i,
          /\breturn\b/i,
          /\bwhile\b/i,
          /\bfor\b/i,
          /\bif\b/i,
          /[[\]]/, // No arrays
          /[{}]/, // No objects
          /\bvar\b/i,
          /\blet\b/i,
          /\bconst\b/i,
          /\bassign\b/i,
          /=/, // No assignments
          /;/, // No semicolons
        ];

        for (const pattern of dangerousPatterns) {
          if (pattern.test(cleanExpression)) {
            return `Error: Invalid expression. Expression contains restricted keywords or characters.`;
          }
        }

        // Additional validation: only allow safe mathematical characters
        const allowedCharacters = /^[0-9+\-*/().\s^a-zA-Z_,]*$/;
        if (!allowedCharacters.test(cleanExpression)) {
          return `Error: Expression contains invalid characters. Only mathematical operations, functions, and numbers are allowed.`;
        }

        // Evaluate the expression using mathjs
        const result = evaluate(cleanExpression);

        // Format the result appropriately
        if (typeof result === "number") {
          if (Number.isInteger(result)) {
            return `Result: ${result}`;
          } else {
            // Round to 10 decimal places to avoid floating point precision issues
            const rounded = Math.round(result * 10000000000) / 10000000000;
            return `Result: ${rounded}`;
          }
        } else if (typeof result === "bigint") {
          return `Result: ${result.toString()}`;
        } else if (
          result &&
          typeof result === "object" &&
          "toString" in result
        ) {
          // Handle complex numbers, units, matrices, etc.
          return `Result: ${result.toString()}`;
        } else {
          return `Result: ${String(result)}`;
        }
      } catch (error: any) {
        // Handle mathjs evaluation errors
        if (error.message) {
          if (error.message.includes("Unexpected type of argument")) {
            return `Error: Invalid mathematical expression. Please check your syntax.`;
          } else if (error.message.includes("Undefined symbol")) {
            return `Error: Unknown function or variable in expression. Make sure all functions are supported mathematical functions.`;
          } else if (error.message.includes("Value expected")) {
            return `Error: Incomplete expression. Please check parentheses and operators.`;
          } else {
            return `Error: ${error.message}`;
          }
        } else {
          return `Error: Unable to evaluate the mathematical expression. Please check your syntax.`;
        }
      }
    },
  });
}
