"use node";

import { tool } from "ai";
import { z } from "zod";
import { internal } from "../../_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";
import { jsonSchema } from "ai";

// Vector search tool – fetches relevant snippets from the current conversation
export function createContextTool(ctx: any) {
  return tool({
    description:
      "Search through the user's conversation history to find relevant context, information, or previous discussions",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        query: {
          type: "string",
          description:
            "Search query to find relevant conversations or messages",
        },
        user_id: {
          type: "string",
          description:
            "Optional user ID to search within specific user's conversations (leave empty for all users)",
        },
        conversation_id: {
          type: "string",
          description:
            "Optional conversation ID to search within a specific conversation (leave empty for all conversations)",
        },
        branch_id: {
          type: "string",
          description:
            "Optional branch ID to search within a specific conversation branch (leave empty for all branches)",
        },
        top_k: {
          type: "number",
          description: "Number of results to return (default: 5)",
        },
      },
      required: ["query", "user_id", "conversation_id", "branch_id", "top_k"],
      additionalProperties: false,
    }),
    execute: async (args: any) => {
      const { query } = args;
      let {
        user_id = "",
        conversation_id = "",
        branch_id = "",
        top_k = 5,
      } = args;

      // Ensure defaults for empty values
      if (!user_id) {
        user_id = "";
      }
      if (!conversation_id) {
        conversation_id = "";
      }
      if (!branch_id) {
        branch_id = "";
      }
      if (!top_k || top_k <= 0) {
        top_k = 5;
      }

      // Handle empty query
      if (!query || query.trim() === "") {
        return `Error: No search query provided for context search.

Please provide a specific search query such as:
- "user preferences"
- "project details"
- "previous discussions about AI"
- "mentioned locations or dates"`;
      }

      let uid = user_id;
      if (!uid) {
        // try to resolve from auth context
        uid = (await getAuthUserId(ctx)) as unknown as string;
      }

      if (!uid) {
        return "❌ **Context Search Error: Authentication Required**\n\nCannot search context without a valid user ID and no authenticated user found.\n\nPlease ensure you're logged in or provide a valid user_id parameter.";
      }

      const results = await ctx.runAction(
        (internal as any).vectorize.searchContext,
        {
          userId: uid,
          conversationId: conversation_id || undefined,
          branchId: branch_id || undefined,
          query: query.trim(),
          topK: top_k,
        }
      );

      if (!results?.length) {
        return "No relevant context found.";
      }

      return results
        .map(
          (r: any, i: number) =>
            `#${i + 1} (${r.score.toFixed(3)}) [${r.role}] ${r.content}`
        )
        .join("\n\n");
    },
  });
}
