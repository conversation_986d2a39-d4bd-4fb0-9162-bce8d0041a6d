"use node";

import { tool } from "ai";
import { z } from "zod";
import { api } from "../../_generated/api";
import { jsonSchema } from "ai";

export function createMemoryTool(ctx: any) {
  return tool({
    description:
      "Store or retrieve important information from long-term conversation memory. Use PROACTIVELY to check for relevant user information before asking questions the user might have already answered in previous conversations. Always search for relevant memories when handling user preferences, personal details, locations, or ongoing projects.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        action: {
          type: "string",
          enum: ["store", "retrieve", "search"],
          description:
            "Whether to store information, retrieve by exact key, or search across all memories for relevant information",
        },
        key: {
          type: "string",
          description:
            "For store/retrieve: The specific topic/category of information (e.g., 'location', 'preferences', 'project_status'). For search: Keywords to look for in any memory. Leave empty if not applicable.",
        },
        value: {
          type: "string",
          description:
            "The important information to store for future conversations (required for store action, leave empty for retrieve/search)",
        },
      },
      required: ["action", "key", "value"],
      additionalProperties: false,
    }),
    execute: async (args: any) => {
      const { action } = args;
      let { key = "", value = "" } = args;

      // Ensure key and value are strings
      if (!key) {
        key = "";
      }
      if (!value) {
        value = "";
      }

      // Handle empty key for actions that require it
      if (
        (action === "store" || action === "retrieve" || action === "search") &&
        key.trim() === ""
      ) {
        return `Error: Key is required for ${action} action.

Examples:
- store: key="user_preferences", value="likes dark mode, prefers TypeScript"
- retrieve: key="user_location"
- search: key="project" (searches for any memories containing "project")`;
      }

      if (action === "store") {
        if (!value || value.trim() === "") {
          return "Error: Value is required for store action. Please provide the information to store.";
        }
        if (!key) {
          return "Error: Key is required for store action.";
        }
        await ctx.runMutation(api.userMemories.add, {
          memory: `${key.trim()}: ${value.trim()}`,
        });
        return `Stored information for future reference - "${key.trim()}": ${value.trim()}`;
      } else if (action === "retrieve" && key) {
        try {
          const memories = await ctx.runQuery(api.userMemories.list);
          const memory = memories.find((m: any) =>
            m.memory.toLowerCase().startsWith(`${key.toLowerCase()}:`)
          );
          return memory
            ? `Retrieved stored information for "${key}": ${memory.memory.substring(memory.memory.indexOf(":") + 1).trim()}`
            : `No stored information found for "${key}"`;
        } catch {
          return `No stored information found for "${key}"`;
        }
      } else if (action === "search" && key) {
        try {
          const memories = await ctx.runQuery(api.userMemories.list);
          const matchingMemories = memories.filter((m: any) =>
            m.memory.toLowerCase().includes(key.toLowerCase())
          );

          if (matchingMemories.length > 0) {
            return `Found ${matchingMemories.length} relevant memories:\n${matchingMemories.map((m: any) => `- ${m.memory}`).join("\n")}`;
          }
          return `No memories found containing "${key}"`;
        } catch {
          return `Error searching memories for "${key}"`;
        }
      }
      return "Invalid memory operation - specify 'store', 'retrieve', or 'search' action with appropriate parameters";
    },
  });
}
