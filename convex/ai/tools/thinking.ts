"use node";

import { tool } from "ai";
import { jsonSchema } from "ai";
import { api } from "../../_generated/api";
import { getProviderApiKey, createAIModel } from "../providers";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { generateText } from "ai";

export function createThinkingTool(ctx: any, enabledTools: string[] = []) {
  return tool({
    description: `Advanced reasoning tool that engages in iterative, step-by-step thinking similar to o1-style reasoning models. Use this tool when you need to:

- Work through complex problems that require multiple steps
- Reason about ambiguous or challenging questions  
- Decompose complex scenarios into manageable parts
- Think through multiple possibilities and their implications
- Verify your reasoning and check for errors
- Explore different approaches to a problem
- Reason about cause and effect relationships
- Work through logical chains of reasoning

This tool will engage in actual iterative thinking, not just provide templates. It will break down problems, explore different angles, check its work, and refine its understanding step by step.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        problem: {
          type: "string",
          description:
            "The complex problem, question, or scenario that requires deep, iterative reasoning to understand and solve",
        },
        focus_areas: {
          type: "array",
          items: { type: "string" },
          description:
            "Specific aspects of the problem to focus on (e.g. 'technical feasibility', 'cost implications', 'user impact'). Leave empty for general reasoning.",
        },
        reasoning_depth: {
          type: "string",
          enum: ["quick", "standard", "deep", "thorough"],
          description:
            "How deeply to reason about the problem. Use 'standard' for default depth.",
        },
      },
      required: ["problem", "focus_areas", "reasoning_depth"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { problem, focus_areas = [], reasoning_depth = "standard" } = args;

      if (!problem || problem.trim() === "") {
        return `# ❌ Thinking Error

**Issue:** No problem provided for reasoning through.

**I need a specific problem, question, or scenario to think through step by step.**

**Examples of what I can reason about:**
- "Should I choose React or Vue for my new project?"
- "How can I optimize this algorithm that's running too slowly?"
- "What are the implications of implementing this new feature?"
- "How should I approach this complex technical decision?"
- "What's the best way to solve this multi-constraint optimization problem?"`;
      }

      const depth = reasoning_depth || "standard";
      const areas: string[] = focus_areas || [];

      // Choose fast model for reasoning
      const provider = "groq";
      const model = "llama-3.1-8b-instant";

      // Get API key
      const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider,
      });
      const { apiKey } = getProviderApiKey(provider, apiKeyRecord);

      if (!apiKey) {
        return "Error: No Groq API key available for reasoning tool.";
      }

      // Create AI model
      const { model: aiModel } = createAIModel({
        provider,
        model,
        apiKey,
        baseURL: PROVIDER_BASE_URLS[provider],
      });

      // Create refined prompt for natural reasoning
      const reasoningPrompt = `You are an expert reasoner thinking through a complex problem in a natural, stream-of-consciousness style, similar to human internal monologue.

Problem: ${problem}

${areas.length > 0 ? `Focus on these areas: ${areas.join(", ")}` : ""}

**Available Active Tools**: ${enabledTools.length > 0 ? enabledTools.join(", ") : "No tools currently enabled"}

Reason at a ${depth} level. Show your thought process organically:

- Start with initial reactions and breakdowns
- Explore ideas, question assumptions, consider alternatives
- Consider what tools might be helpful from the available active tools
- Build understanding gradually
- End with synthesized conclusions

Think naturally, like: "Okay, first I'm considering... Hmm, that leads to... Wait, but what if... Oh, that changes things..."

Output ONLY the reasoning monologue. No introductions or headings.`;

      const { text } = await generateText({
        model: aiModel,
        prompt: reasoningPrompt,
        temperature: 0.7,
        maxOutputTokens: depth === "thorough" ? 15000 : 10000,
      });

      return text || "No reasoning generated.";
    },
  });
}
