"use node";

import { tool } from "ai";
import { z } from "zod";
import { IMAGE_PROVIDERS, ImageModelId } from "../utils/imageProviders";
import { IMAGE_MODELS } from "../../../src/lib/models";
import { api } from "../../_generated/api";
import { jsonSchema } from "ai";

export function createImageGenerationTool(ctx: any) {
  return tool({
    description:
      "Generate an image based on a text prompt. Creates high-quality images using AI and returns the image URL for display in the chat. Different providers have different costs - check your credits before generating.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description:
            "A detailed description of the image to generate. Be specific about style, colors, composition, and any important details.",
        },
        style: {
          type: "string",
          enum: [
            "realistic",
            "artistic",
            "anime",
            "cartoon",
            "digital_art",
            "oil_painting",
            "watercolor",
            "sketch",
            "vintage",
            "cyberpunk",
            "minimalist",
            "abstract",
          ],
          description:
            "Optional style for the image. Use 'realistic' for default style.",
        },
        aspectRatio: {
          type: "string",
          enum: ["1:1", "16:9", "9:16", "4:3", "3:4"],
          description:
            "Optional aspect ratio for the image. Use '1:1' for default.",
        },
      },
      required: ["prompt", "style", "aspectRatio"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { prompt, style = "realistic", aspectRatio = "1:1" } = args;
      // Handle empty prompt
      if (!prompt || prompt.trim() === "") {
        return `Error: No prompt provided for image generation.

Please provide a detailed description such as:
- "A sunset over mountains with vibrant orange and purple clouds"
- "A modern minimalist office space with natural lighting"
- "A cartoon-style friendly robot with blue and silver colors"
- "An oil painting of a peaceful forest scene in autumn"`;
      }

      // Handle defaults and convert null to undefined for API compatibility
      const actualStyle = style || "realistic";
      const actualAspectRatio = aspectRatio || "1:1";

      try {
        // Get user preferences to determine which image model to use
        const preferences = await ctx.runQuery(api.preferences.get);
        const imageModel: ImageModelId =
          (preferences?.imageModel as ImageModelId) || "flux-1-schnell"; // Default to flux-1-schnell

        const provider = IMAGE_PROVIDERS[imageModel];
        if (!provider) {
          throw new Error(`Unknown image model: ${imageModel}`);
        }

        // Check if this provider requires an API key and if we're using user's key
        let usingUserKey = false;
        if (provider.requiresApiKey) {
          // For future providers that require API keys, we can add logic here
          // Currently Cloudflare models don't require user API keys
          usingUserKey = false;
        }

        // If using built-in keys, check credits first
        if (!usingUserKey) {
          const modelPrice = IMAGE_MODELS[imageModel]?.pricing || 0.05; // Default price

          const creditsCheck = await ctx.runQuery(
            api.usage.checkImageCreditsAvailable,
            {
              imageModel,
            }
          );

          if (!creditsCheck.hasCredits) {
            return `❌ **Insufficient Credits for Image Generation**

You need ${creditsCheck.requiredCredits} credits but only have ${creditsCheck.availableCredits} available.

**💡 Solutions:**
- Add your own ${provider.name} API key for unlimited usage
- Wait until next month when limits reset
- The image costs $${modelPrice.toFixed(3)} (${creditsCheck.requiredCredits} credits)`;
          }
        }

        // Generate the image - convert null values to undefined for API compatibility
        const result = await provider.generateImage(
          ctx,
          {
            prompt: prompt.trim(),
            style: actualStyle === "realistic" ? undefined : actualStyle,
            aspectRatio:
              actualAspectRatio === "1:1" ? undefined : actualAspectRatio,
            modelId: imageModel,
          },
          usingUserKey
        );

        // Deduct credits only if using built-in keys
        if (!usingUserKey) {
          await ctx.runMutation(api.usage.deductImageCredits, {
            imageModel,
          });
        }

        return result;
      } catch (error) {
        console.error("Image generation error:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        return `❌ **Image Generation Failed**

Error: ${errorMessage}

**💡 Try:**
- Rephrasing your prompt
- Checking your API key configuration in Settings
- Using a different image model from Settings → AI & Behavior`;
      }
    },
  });
}
