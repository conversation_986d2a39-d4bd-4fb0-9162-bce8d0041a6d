"use node";

import { tool } from "ai";
import { jsonSchema } from "ai";

export function createDateTimeTool() {
  return tool({
    description:
      "Advanced date/time operations and calculations. Use when you need current date/time, date parsing, time calculations, or timezone conversions. This tool is intelligent about ambiguous date formats and will ask for clarification when needed.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        action: {
          type: "string",
          enum: [
            "current_datetime",
            "current_date",
            "current_time",
            "parse_date",
            "timezone_convert",
            "date_add",
            "date_subtract",
            "date_diff",
            "format_date",
            "validate_date",
            "get_weekday",
            "days_until",
            "time_ago",
          ],
          description: "The specific datetime operation to perform",
        },
        date_input: {
          type: "string",
          description:
            "Date to parse or work with. Can be various formats like 'January 2, 2025', '2025-01-02', '01/02/2025', etc. For ambiguous formats, specify date_format_preference. Leave empty if not needed.",
        },
        date_format_preference: {
          type: "string",
          enum: ["US", "European", "ISO", "auto"],
          description:
            "How to interpret ambiguous dates like '01/02/2025'. US = MM/DD/YYYY, European = DD/MM/YYYY, ISO = YYYY-MM-DD, auto = best guess. Use 'auto' for default.",
        },
        target_date: {
          type: "string",
          description:
            "Second date for comparisons or calculations. Leave empty if not needed.",
        },
        amount: {
          type: "number",
          description:
            "Number of units to add/subtract (e.g., 5 for 5 days). Use 0 if not needed.",
        },
        unit: {
          type: "string",
          enum: [
            "years",
            "months",
            "weeks",
            "days",
            "hours",
            "minutes",
            "seconds",
          ],
          description: "Time unit for calculations. Use 'days' for default.",
        },
        timezone: {
          type: "string",
          description:
            "Timezone (e.g., 'America/New_York', 'Europe/London', 'UTC'). Leave empty for local timezone.",
        },
        target_timezone: {
          type: "string",
          description:
            "Target timezone for conversions. Leave empty if not needed.",
        },
        output_format: {
          type: "string",
          enum: ["full", "date", "time", "iso", "relative", "custom"],
          description: "How to format the output. Use 'full' for default.",
        },
      },
      required: [
        "action",
        "date_input",
        "date_format_preference",
        "target_date",
        "amount",
        "unit",
        "timezone",
        "target_timezone",
        "output_format",
      ],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const {
        action,
        date_input = "",
        date_format_preference = "auto",
        target_date = "",
        amount = 0,
        unit = "days",
        timezone = "",
        target_timezone = "",
        output_format = "full",
      } = args;

      try {
        const now = new Date();

        switch (action) {
          case "current_datetime":
          case "current_date":
          case "current_time": {
            const targetTz =
              timezone || Intl.DateTimeFormat().resolvedOptions().timeZone;

            if (action === "current_date") {
              return formatDateOutput(now, "date", targetTz);
            } else if (action === "current_time") {
              return formatDateOutput(now, "time", targetTz);
            } else {
              return formatDateOutput(now, output_format || "full", targetTz);
            }
          }

          case "parse_date": {
            if (!date_input) {
              return `Error: No date provided to parse.

Please provide a date in formats like:
- "January 2, 2025"
- "2025-01-02" 
- "01/02/2025"
- "tomorrow"
- "next Friday"
- "2 weeks ago"

For ambiguous formats like "01/02/2025", specify date_format_preference as "US" (MM/DD/YYYY) or "European" (DD/MM/YYYY).`;
            }

            const parseResult = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            if (parseResult.ambiguous) {
              return `⚠️ **Ambiguous Date Format Detected**

"${date_input}" could be interpreted as:
${parseResult.interpretations.map((interp: any, i: number) => `${i + 1}. ${interp.description} → ${formatDateOutput(interp.date, "full")}`).join("\n")}

Please clarify by:
- Using a clearer format like "January 2, 2025" or "2025-01-02"
- Or specify date_format_preference as "US" (MM/DD/YYYY) or "European" (DD/MM/YYYY)`;
            }

            if (parseResult.error) {
              return `Error parsing date "${date_input}": ${parseResult.error}

Try formats like:
- "January 2, 2025"
- "2025-01-02"
- "01/02/2025" (specify US or European format)
- "tomorrow", "next week", "2 days ago"`;
            }

            return `Parsed date: ${formatDateOutput(parseResult.date, output_format || "full")}
Original input: "${date_input}"
Interpretation: ${parseResult.interpretation}`;
          }

          case "date_add":
          case "date_subtract": {
            if (
              !date_input ||
              amount === null ||
              amount === undefined ||
              !unit
            ) {
              return `Error: Missing required parameters for ${action}.

Required:
- date_input: The starting date
- amount: Number to add/subtract (e.g., 5)
- unit: Time unit (years, months, weeks, days, hours, minutes, seconds)

Example: Add 5 days to "2025-01-02"`;
            }

            const baseDate = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            if (baseDate.error || baseDate.ambiguous) {
              return `Error parsing base date: ${baseDate.error || "Ambiguous date format"}`;
            }

            const result = addSubtractDate(
              baseDate.date,
              amount,
              unit,
              action === "date_subtract"
            );
            return `${action === "date_add" ? "Added" : "Subtracted"} ${amount} ${unit} ${action === "date_add" ? "to" : "from"} ${date_input}:
Result: ${formatDateOutput(result, output_format || "full")}`;
          }

          case "date_diff": {
            if (!date_input || !target_date) {
              return `Error: Both date_input and target_date are required for date difference calculation.

Example: Calculate difference between "2025-01-01" and "2025-01-15"`;
            }

            const date1 = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            const date2 = parseDateIntelligently(
              target_date,
              date_format_preference
            );

            if (date1.error || date1.ambiguous) {
              return `Error parsing first date: ${date1.error || "Ambiguous format"}`;
            }
            if (date2.error || date2.ambiguous) {
              return `Error parsing second date: ${date2.error || "Ambiguous format"}`;
            }

            const diff = calculateDateDifference(date1.date, date2.date);
            return `Time difference between ${formatDateOutput(date1.date, "date")} and ${formatDateOutput(date2.date, "date")}:
${diff.description}
Exact difference: ${diff.totalDays} days (${diff.totalHours} hours, ${diff.totalMinutes} minutes)`;
          }

          case "timezone_convert": {
            if (!date_input || !target_timezone) {
              return `Error: Both date_input and target_timezone are required for timezone conversion.

Example: Convert "2025-01-02 15:30" from "America/New_York" to "Europe/London"`;
            }

            const baseDate = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            if (baseDate.error || baseDate.ambiguous) {
              return `Error parsing date: ${baseDate.error || "Ambiguous format"}`;
            }

            const sourceTz =
              timezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
            const converted = convertTimezone(
              baseDate.date,
              sourceTz,
              target_timezone
            );

            return `Timezone conversion:
Source: ${formatDateOutput(baseDate.date, "full", sourceTz)} (${sourceTz})
Target: ${formatDateOutput(converted, "full", target_timezone)} (${target_timezone})`;
          }

          case "get_weekday": {
            const targetDate = date_input
              ? parseDateIntelligently(date_input, date_format_preference).date
              : now;
            const weekday = targetDate.toLocaleDateString("en-US", {
              weekday: "long",
            });
            const dateStr = formatDateOutput(targetDate, "date");
            return `${dateStr} is a ${weekday}`;
          }

          case "days_until": {
            if (!date_input) {
              return `Error: date_input required to calculate days until.

Example: Days until "2025-12-25"`;
            }

            const targetDate = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            if (targetDate.error || targetDate.ambiguous) {
              return `Error parsing target date: ${targetDate.error || "Ambiguous format"}`;
            }

            const diff = Math.ceil(
              (targetDate.date.getTime() - now.getTime()) /
                (1000 * 60 * 60 * 24)
            );
            const absiff = Math.abs(diff);

            if (diff > 0) {
              return `${diff} days until ${formatDateOutput(targetDate.date, "date")} (${formatDateOutput(targetDate.date, "full")})`;
            } else if (diff < 0) {
              return `${formatDateOutput(targetDate.date, "date")} was ${absiff} days ago (${formatDateOutput(targetDate.date, "full")})`;
            } else {
              return `${formatDateOutput(targetDate.date, "date")} is today!`;
            }
          }

          case "validate_date": {
            if (!date_input) {
              return `Error: date_input required for validation.`;
            }

            const result = parseDateIntelligently(
              date_input,
              date_format_preference
            );
            if (result.ambiguous) {
              return `⚠️ Date is ambiguous: "${date_input}"
Possible interpretations:
${result.interpretations.map((interp: any, i: number) => `${i + 1}. ${interp.description}`).join("\n")}`;
            }

            if (result.error) {
              return `❌ Invalid date: "${date_input}"
Error: ${result.error}`;
            }

            return `✅ Valid date: "${date_input}"
Parsed as: ${formatDateOutput(result.date, "full")}
Interpretation: ${result.interpretation}`;
          }

          default:
            return `Error: Unknown action "${action}". Available actions: current_datetime, current_date, current_time, parse_date, timezone_convert, date_add, date_subtract, date_diff, format_date, validate_date, get_weekday, days_until, time_ago`;
        }
      } catch (error) {
        return `Error in datetime operation: ${error instanceof Error ? error.message : "Unknown error"}`;
      }
    },
  });
}

// Helper functions for intelligent date parsing and formatting

function parseDateIntelligently(
  dateStr: string,
  formatPreference?: string | null
): any {
  const input = dateStr.trim();

  // Handle relative dates first
  const relativeResult = parseRelativeDate(input);
  if (relativeResult) {
    return { date: relativeResult, interpretation: `Relative date: ${input}` };
  }

  // Handle natural language dates
  const naturalResult = parseNaturalDate(input);
  if (naturalResult) {
    return {
      date: naturalResult,
      interpretation: `Natural language: ${input}`,
    };
  }

  // Handle ISO format (unambiguous)
  if (/^\d{4}-\d{1,2}-\d{1,2}/.test(input)) {
    const date = new Date(input);
    if (!isNaN(date.getTime())) {
      return { date, interpretation: "ISO format (YYYY-MM-DD)" };
    }
  }

  // Handle ambiguous formats like 01/02/2025, 01-02-2025
  const ambiguousMatch = input.match(/^(\d{1,2})[/-](\d{1,2})[/-](\d{4})$/);
  if (ambiguousMatch) {
    const [_, part1, part2, year] = ambiguousMatch;
    const interpretations = [];

    // US format (MM/DD/YYYY)
    const usDate = new Date(
      parseInt(year),
      parseInt(part1) - 1,
      parseInt(part2)
    );
    if (
      !isNaN(usDate.getTime()) &&
      parseInt(part1) <= 12 &&
      parseInt(part2) <= 31
    ) {
      interpretations.push({
        date: usDate,
        description: `US format (${part1}/${part2}/${year} = Month ${part1}, Day ${part2})`,
        format: "US",
      });
    }

    // European format (DD/MM/YYYY)
    const euroDate = new Date(
      parseInt(year),
      parseInt(part2) - 1,
      parseInt(part1)
    );
    if (
      !isNaN(euroDate.getTime()) &&
      parseInt(part2) <= 12 &&
      parseInt(part1) <= 31
    ) {
      interpretations.push({
        date: euroDate,
        description: `European format (${part1}/${part2}/${year} = Day ${part1}, Month ${part2})`,
        format: "European",
      });
    }

    if (interpretations.length > 1 && !formatPreference) {
      return { ambiguous: true, interpretations };
    }

    // Return based on preference
    if (formatPreference === "US" || formatPreference === "auto") {
      const usInterp = interpretations.find((i) => i.format === "US");
      if (usInterp)
        return { date: usInterp.date, interpretation: usInterp.description };
    }

    if (formatPreference === "European") {
      const euroInterp = interpretations.find((i) => i.format === "European");
      if (euroInterp)
        return {
          date: euroInterp.date,
          interpretation: euroInterp.description,
        };
    }

    // Default to first valid interpretation
    if (interpretations.length > 0) {
      return {
        date: interpretations[0].date,
        interpretation: interpretations[0].description,
      };
    }
  }

  // Try standard Date constructor as fallback
  const fallbackDate = new Date(input);
  if (!isNaN(fallbackDate.getTime())) {
    return { date: fallbackDate, interpretation: "Standard date parsing" };
  }

  return { error: `Unable to parse "${input}" as a valid date` };
}

function parseRelativeDate(input: string): Date | null {
  const now = new Date();
  const lower = input.toLowerCase();

  if (lower === "today" || lower === "now") return now;
  if (lower === "tomorrow")
    return new Date(now.getTime() + 24 * 60 * 60 * 1000);
  if (lower === "yesterday")
    return new Date(now.getTime() - 24 * 60 * 60 * 1000);

  // Handle "X days/weeks/months ago" or "in X days/weeks/months"
  const relativeMatch = lower.match(
    /^(?:in\s+)?(\d+)\s+(day|week|month|year)s?\s*(?:ago|from\s+now)?$/
  );
  if (relativeMatch) {
    const amount = parseInt(relativeMatch[1]);
    const unit = relativeMatch[2];
    const isAgo = lower.includes("ago");

    return addSubtractDate(now, amount, unit + "s", isAgo);
  }

  return null;
}

function parseNaturalDate(input: string): Date | null {
  const lower = input.toLowerCase();
  const now = new Date();

  // Handle "next/last Friday" etc.
  const weekdays = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];
  const weekdayMatch = lower.match(
    /^(next|last)\s+(sunday|monday|tuesday|wednesday|thursday|friday|saturday)$/
  );

  if (weekdayMatch) {
    const direction = weekdayMatch[1];
    const targetDay = weekdays.indexOf(weekdayMatch[2]);
    const currentDay = now.getDay();

    let daysToAdd = targetDay - currentDay;
    if (direction === "next") {
      if (daysToAdd <= 0) daysToAdd += 7;
    } else {
      if (daysToAdd >= 0) daysToAdd -= 7;
    }

    return new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
  }

  return null;
}

function addSubtractDate(
  date: Date,
  amount: number,
  unit: string,
  subtract: boolean = false
): Date {
  const result = new Date(date);
  const multiplier = subtract ? -1 : 1;

  switch (unit.toLowerCase()) {
    case "years":
      result.setFullYear(result.getFullYear() + amount * multiplier);
      break;
    case "months":
      result.setMonth(result.getMonth() + amount * multiplier);
      break;
    case "weeks":
      result.setDate(result.getDate() + amount * 7 * multiplier);
      break;
    case "days":
      result.setDate(result.getDate() + amount * multiplier);
      break;
    case "hours":
      result.setHours(result.getHours() + amount * multiplier);
      break;
    case "minutes":
      result.setMinutes(result.getMinutes() + amount * multiplier);
      break;
    case "seconds":
      result.setSeconds(result.getSeconds() + amount * multiplier);
      break;
  }

  return result;
}

function calculateDateDifference(date1: Date, date2: Date): any {
  const diffMs = Math.abs(date2.getTime() - date1.getTime());
  const totalMinutes = Math.floor(diffMs / (1000 * 60));
  const totalHours = Math.floor(diffMs / (1000 * 60 * 60));
  const totalDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  const years = Math.floor(totalDays / 365);
  const months = Math.floor((totalDays % 365) / 30);
  const days = totalDays % 30;

  let description = "";
  if (years > 0) description += `${years} year${years !== 1 ? "s" : ""} `;
  if (months > 0) description += `${months} month${months !== 1 ? "s" : ""} `;
  if (days > 0) description += `${days} day${days !== 1 ? "s" : ""}`;

  if (!description) description = "Less than a day";

  return {
    description: description.trim(),
    totalDays,
    totalHours,
    totalMinutes,
  };
}

function convertTimezone(date: Date, fromTz: string, toTz: string): Date {
  // This is a simplified conversion - in production you'd want a proper timezone library
  const utc = date.getTime() + date.getTimezoneOffset() * 60000;
  return new Date(utc);
}

function formatDateOutput(
  date: Date,
  format: string = "full",
  timezone?: string
): string {
  const options: Intl.DateTimeFormatOptions = {
    ...(timezone && { timeZone: timezone }),
  };

  switch (format) {
    case "date":
      return date.toLocaleDateString("en-US", {
        ...options,
        year: "numeric",
        month: "long",
        day: "numeric",
        weekday: "long",
      });
    case "time":
      return date.toLocaleTimeString("en-US", {
        ...options,
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        timeZoneName: "short",
      });
    case "iso":
      return date.toISOString();
    case "full":
    default:
      return date.toLocaleString("en-US", {
        ...options,
        year: "numeric",
        month: "long",
        day: "numeric",
        weekday: "long",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        timeZoneName: "short",
      });
  }
}
