import { tool } from "ai";
import { z } from "zod";
import { jsonSchema } from "ai";

export function createCodeAnalysisTool() {
  return tool({
    description:
      "Analyze code for bugs, performance issues, best practices, and provide refactoring suggestions. Use ONLY when users provide actual code that needs review, debugging, or optimization. Do NOT use for general programming questions, explanations, or code you can review directly in conversation.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        code: {
          type: "string",
          description: "The actual code to analyze and review.",
        },
        language: {
          type: "string",
          description:
            "Programming language of the code (e.g., javascript, python, java). Leave empty for auto-detection.",
        },
        analysis_focus: {
          type: "string",
          enum: [
            "bugs",
            "performance",
            "security",
            "style",
            "refactor",
            "comprehensive",
          ],
          description:
            "Specific aspect of code analysis to focus on. Use 'comprehensive' for overall analysis.",
        },
      },
      required: ["code", "language", "analysis_focus"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { code } = args;
      let { language = "auto-detect", analysis_focus = "comprehensive" } = args;

      // Handle empty code
      if (!code || code.trim() === "") {
        return `Error: No code provided for analysis.

Please provide the actual code you want me to analyze.

Examples:
- JavaScript function that needs optimization
- Python script with potential bugs
- SQL query that needs security review
- Any code snippet requiring review`;
      }

      // Set defaults
      if (!language) {
        language = "auto-detect";
      }
      if (!analysis_focus) {
        analysis_focus = "comprehensive";
      }

      const trimmedCode = code.trim();
      const lines = trimmedCode.split("\n");
      const lineCount = lines.length;

      // Create analysis prompt for the AI
      let analysisPrompt = `# Code Analysis Request

**Analysis Focus:** ${analysis_focus}
**Language:** ${language}
**Lines of Code:** ${lineCount}

## Code to Analyze:
\`\`\`${language !== "auto-detect" ? language : ""}
${trimmedCode}
\`\`\`

## Analysis Instructions:

Please provide a thorough ${analysis_focus} analysis of the above code. `;

      switch (analysis_focus) {
        case "bugs":
          analysisPrompt += `Focus specifically on:
- Potential runtime errors
- Logic errors
- Exception handling issues
- Type-related problems
- Edge cases that might break
- Missing error checks
- Unsafe operations

Provide specific line numbers where issues exist and suggest concrete fixes.`;
          break;

        case "performance":
          analysisPrompt += `Focus specifically on:
- Time complexity issues (O(n²) loops, etc.)
- Memory usage problems
- Inefficient algorithms or data structures
- Repeated computations
- Database query optimization
- DOM manipulation inefficiencies
- Resource usage patterns

Suggest specific optimizations with before/after examples where helpful.`;
          break;

        case "security":
          analysisPrompt += `Focus specifically on:
- Input validation vulnerabilities
- SQL injection risks
- XSS vulnerabilities
- Authentication/authorization flaws
- Data exposure risks
- Unsafe operations
- Cryptographic issues
- Access control problems

Rate each security issue by severity (Critical/High/Medium/Low) and provide mitigation strategies.`;
          break;

        case "style":
          analysisPrompt += `Focus specifically on:
- Code readability and formatting
- Naming conventions
- Function/class organization
- Comment quality and documentation
- Consistent coding patterns
- Code structure and modularity
- Best practices for the language

Suggest improvements that enhance maintainability and team collaboration.`;
          break;

        case "refactor":
          analysisPrompt += `Focus specifically on:
- Code duplication elimination
- Function/class extraction opportunities
- Design pattern applications
- Architecture improvements
- Simplification possibilities
- Separation of concerns
- Single responsibility principle
- Dependencies and coupling

Provide specific refactoring suggestions with clear benefits explained.`;
          break;

        case "comprehensive":
          analysisPrompt += `Provide a complete analysis covering:

1. **Bug Detection**: Find potential errors, edge cases, and exception handling issues
2. **Security Review**: Identify vulnerabilities and security risks
3. **Performance Analysis**: Spot inefficiencies and optimization opportunities  
4. **Code Quality**: Assess readability, structure, and maintainability
5. **Refactoring Opportunities**: Suggest improvements to design and organization

For each category, provide:
- Specific issues found (with line numbers if applicable)
- Severity/impact assessment  
- Concrete suggestions for improvement
- Benefits of implementing the changes

Prioritize the most critical issues first.`;
          break;
      }

      analysisPrompt += `

## Output Format:
Please structure your analysis with clear headings, specific examples, and actionable recommendations. Use markdown formatting for better readability.`;

      return analysisPrompt;
    },
  });
}
