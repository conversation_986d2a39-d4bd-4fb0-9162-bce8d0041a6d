"use node";
import { Redis } from "@upstash/redis";
import { simulateReadableStream } from "ai";
// Node 18+ exposes TransformStream globally; no need to import.

// Basic Redis client using environment variables. Ensure KV_URL and KV_TOKEN are set in your Convex env.
const redis = new Redis({
  url: process.env.KV_URL!,
  token: process.env.KV_TOKEN!,
});

export const cacheMiddleware = {
  // Cache for non-streaming generate calls
  wrapGenerate: async ({ doGenerate, params }: any) => {
    const cacheKey = JSON.stringify(params);

    let cached: any = null;
    try {
      cached = await redis.get(cacheKey);
    } catch (err) {
      console.warn("Cache read failed, proceeding without cache:", err);
    }

    if (cached !== null) {
      // Hydrate Date instances
      if (cached.response?.timestamp) {
        cached.response.timestamp = new Date(cached.response.timestamp);
      }
      return cached;
    }

    const result = await doGenerate();

    // Store the full result for future identical requests (best effort)
    try {
      await redis.set(cacheKey, result);
    } catch (err) {
      console.warn("Cache write failed (generate):", err);
    }
    return result;
  },

  // Cache for streaming calls
  wrapStream: async ({ doStream, params }: any) => {
    const cacheKey = JSON.stringify(params);

    let cached: any[] | null = null;
    try {
      cached = (await redis.get(cacheKey)) as any[] | null;
    } catch (err) {
      console.warn("Cache read failed (stream), streaming live:", err);
    }

    if (cached !== null) {
      const formatted = cached.map((p) => {
        if (p.type === "response-metadata" && p.timestamp) {
          return { ...p, timestamp: new Date(p.timestamp) };
        }
        return p;
      });

      return {
        stream: simulateReadableStream({
          initialDelayInMs: 0,
          chunkDelayInMs: 10,
          chunks: formatted,
        }),
      };
    }

    const { stream, ...rest } = await doStream();

    const collected: any[] = [];

    const transform: any = new TransformStream({
      transform(chunk: any, controller: any) {
        collected.push(chunk);
        controller.enqueue(chunk);
      },
      flush() {
        // Persist full stream once finished
        redis.set(cacheKey, collected).catch(() => {
          /* non-critical */
        });
      },
    });

    return {
      stream: stream.pipeThrough(transform),
      ...rest,
    };
  },
};
