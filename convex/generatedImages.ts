import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { query } from "./_generated/server";

export const storeGeneratedImage = mutation({
  args: {
    storageId: v.id("_storage"),
    prompt: v.string(),
    style: v.optional(v.string()),
    aspectRatio: v.optional(v.string()),
    model: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    await ctx.db.insert("generatedImages", {
      userId,
      storageId: args.storageId,
      prompt: args.prompt,
      style: args.style,
      aspectRatio: args.aspectRatio,
      model: args.model,
      generatedAt: Date.now(),
    });
  },
});

export const getGeneratedImages = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const images = await ctx.db
      .query("generatedImages")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();

    return Promise.all(
      images.map(async (image) => {
        const url = await ctx.storage.getUrl(image.storageId);
        return {
          ...image,
          url,
        };
      })
    );
  },
});
