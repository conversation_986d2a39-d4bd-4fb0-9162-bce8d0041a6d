import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import type { Id } from "./_generated/dataModel";

// List n8n servers for the current user
export const list = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    return await ctx.db
      .query("n8nServers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
  },
});

// List enabled servers
export const listEnabled = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    return await ctx.db
      .query("n8nServers")
      .withIndex("by_user_enabled", (q) =>
        q.eq("userId", userId).eq("isEnabled", true)
      )
      .collect();
  },
});

// Add new server
export const add = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    apiUrl: v.string(),
    apiKey: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    return await ctx.db.insert("n8nServers", {
      userId,
      name: args.name,
      description: args.description,
      apiUrl: args.apiUrl.replace(/\/+$/, ""),
      apiKey: args.apiKey,
      isEnabled: true,
      createdAt: Date.now(),
    });
  },
});

// Update server
export const update = mutation({
  args: {
    id: v.id("n8nServers"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    apiUrl: v.optional(v.string()),
    apiKey: v.optional(v.string()),
    isEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    const server = await ctx.db.get(args.id);
    if (!server || server.userId !== userId)
      throw new Error("Server not found");

    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name;
    if (args.description !== undefined) updates.description = args.description;
    if (args.apiUrl !== undefined)
      updates.apiUrl = args.apiUrl.replace(/\/+$/, "");
    if (args.apiKey !== undefined) updates.apiKey = args.apiKey;
    if (args.isEnabled !== undefined) updates.isEnabled = args.isEnabled;
    return await ctx.db.patch(args.id, updates);
  },
});

// Delete server
export const remove = mutation({
  args: { id: v.id("n8nServers") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    const server = await ctx.db.get(args.id);
    if (!server || server.userId !== userId)
      throw new Error("Server not found");
    await ctx.db.delete(args.id);
  },
});

// Toggle enabled
export const toggle = mutation({
  args: { id: v.id("n8nServers") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    const server = await ctx.db.get(args.id);
    if (!server || server.userId !== userId)
      throw new Error("Server not found");
    await ctx.db.patch(args.id, {
      isEnabled: !server.isEnabled,
      lastUsed: server.isEnabled ? undefined : Date.now(),
    });
  },
});

// Cache available tools bound to this server
export const updateToolsCache = mutation({
  args: {
    id: v.id("n8nServers"),
    availableTools: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");
    const server = await ctx.db.get(args.id);
    if (!server || server.userId !== userId)
      throw new Error("Server not found");
    await ctx.db.patch(args.id, {
      availableTools: args.availableTools,
      toolsLastUpdated: Date.now(),
    });
  },
});
