import { v } from "convex/values";
import { internalQuery, internalMutation } from "./_generated/server";
import { STRIPE_PLANS } from "./stripe";

// Internal helpers that don't need Node.js access
export const getUserUsage = internalQuery({
  args: {
    userId: v.id("users"),
  },
  returns: v.union(
    v.object({
      _id: v.id("userUsage"),
      _creationTime: v.number(),
      userId: v.id("users"),
      plan: v.union(
        v.literal("free"),
        v.literal("pro"),
        v.literal("ultra"),
        v.literal("max")
      ),
      creditsUsed: v.number(),
      creditsLimit: v.number(),
      maxSpendingDollars: v.number(),
      dollarsSpent: v.number(),
      searchesUsed: v.number(),
      resetDate: v.number(),
      stripeCustomerId: v.optional(v.string()),
      stripeSubscriptionId: v.optional(v.string()),
      subscriptionStatus: v.optional(v.string()),
      currentPeriodStart: v.optional(v.number()),
      currentPeriodEnd: v.optional(v.number()),
      cancelAtPeriodEnd: v.optional(v.boolean()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();
  },
});

export const getUserDetails = internalQuery({
  args: {
    userId: v.id("users"),
  },
  returns: v.union(
    v.object({
      email: v.optional(v.string()),
      name: v.optional(v.string()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    return user ? { email: user.email, name: user.name } : null;
  },
});

export const updateStripeCustomerId = internalMutation({
  args: {
    userId: v.id("users"),
    stripeCustomerId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      const usage = await ctx.db
        .query("userUsage")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .unique();

      if (!usage) {
        // Create usage record if it doesn't exist
        const resetDate = new Date();
        resetDate.setTime(resetDate.getTime() + 30 * 24 * 60 * 60 * 1000);

        await ctx.db.insert("userUsage", {
          userId: args.userId,
          plan: "free",
          creditsUsed: 0,
          creditsLimit: 100,
          maxSpendingDollars: 1.0,
          dollarsSpent: 0,
          searchesUsed: 0,
          resetDate: resetDate.getTime(),
          stripeCustomerId: args.stripeCustomerId,
        });
      } else {
        await ctx.db.patch(usage._id, {
          stripeCustomerId: args.stripeCustomerId,
        });
      }
    } catch (error) {
      // If this fails due to concurrent creation, try to update instead
      if (String(error).includes("OptimisticConcurrencyControlFailure")) {
        const usage = await ctx.db
          .query("userUsage")
          .withIndex("by_user", (q) => q.eq("userId", args.userId))
          .unique();

        if (usage) {
          await ctx.db.patch(usage._id, {
            stripeCustomerId: args.stripeCustomerId,
          });
        }
      } else {
        throw error;
      }
    }

    return null;
  },
});

export const updateSubscription = internalMutation({
  args: {
    stripeCustomerId: v.string(),
    stripeSubscriptionId: v.string(),
    plan: v.union(v.literal("pro"), v.literal("ultra"), v.literal("max")),
    status: v.string(),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAtPeriodEnd: v.boolean(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Use indexed query for better performance
    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_stripe_customer", (q) =>
        q.eq("stripeCustomerId", args.stripeCustomerId)
      )
      .unique();

    if (!usage) {
      throw new Error(
        `User not found for Stripe customer: ${args.stripeCustomerId}`
      );
    }

    const planConfig = STRIPE_PLANS[args.plan];
    if (!planConfig) {
      throw new Error(`Invalid plan: ${args.plan}`);
    }

    // Perform atomic update with all fields
    await ctx.db.patch(usage._id, {
      plan: args.plan,
      stripeSubscriptionId: args.stripeSubscriptionId,
      subscriptionStatus: args.status as
        | "active"
        | "canceled"
        | "incomplete"
        | "incomplete_expired"
        | "past_due"
        | "trialing"
        | "unpaid",
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAtPeriodEnd: args.cancelAtPeriodEnd,
      creditsLimit: planConfig.credits,
      maxSpendingDollars: planConfig.maxSpendingDollars,
      // Reset usage for new subscription period
      creditsUsed: 0,
      dollarsSpent: 0,
      searchesUsed: 0,
      resetDate: args.currentPeriodEnd,
    });

    return null;
  },
});

export const cancelSubscriptionInDb = internalMutation({
  args: {
    stripeSubscriptionId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_stripe_subscription", (q) =>
        q.eq("stripeSubscriptionId", args.stripeSubscriptionId)
      )
      .unique();

    if (!usage) {
      return null;
    }

    // Reset to free plan
    const resetDate = new Date();
    resetDate.setTime(resetDate.getTime() + 30 * 24 * 60 * 60 * 1000);

    await ctx.db.patch(usage._id, {
      plan: "free",
      subscriptionStatus: "canceled",
      stripeSubscriptionId: undefined,
      currentPeriodStart: undefined,
      currentPeriodEnd: undefined,
      cancelAtPeriodEnd: undefined,
      creditsLimit: 100,
      maxSpendingDollars: 1.0,
      creditsUsed: 0,
      dollarsSpent: 0,
      searchesUsed: 0,
      resetDate: resetDate.getTime(),
    });

    return null;
  },
});

// Webhook event tracking functions
export const getWebhookEvent = internalQuery({
  args: {
    stripeEventId: v.string(),
  },
  returns: v.union(
    v.object({
      _id: v.id("stripeWebhookEvents"),
      stripeEventId: v.string(),
      eventType: v.string(),
      processed: v.boolean(),
      processedAt: v.optional(v.number()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("stripeWebhookEvents")
      .withIndex("by_stripe_event_id", (q) =>
        q.eq("stripeEventId", args.stripeEventId)
      )
      .unique();
  },
});

export const recordWebhookEvent = internalMutation({
  args: {
    stripeEventId: v.string(),
    eventType: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.insert("stripeWebhookEvents", {
      stripeEventId: args.stripeEventId,
      eventType: args.eventType,
      processed: false,
    });
    return null;
  },
});

export const processWebhookEventIdempotent = internalMutation({
  args: {
    stripeEventId: v.string(),
    eventType: v.string(),
    eventData: v.string(),
  },
  returns: v.object({
    alreadyProcessed: v.boolean(),
  }),
  handler: async (ctx, args) => {
    // Check if event already exists using index
    const existingEvent = await ctx.db
      .query("stripeWebhookEvents")
      .withIndex("by_stripe_event_id", (q) =>
        q.eq("stripeEventId", args.stripeEventId)
      )
      .unique();

    if (existingEvent) {
      return { alreadyProcessed: true };
    }

    // Insert new event atomically
    await ctx.db.insert("stripeWebhookEvents", {
      stripeEventId: args.stripeEventId,
      eventType: args.eventType,
      processed: false,
    });

    return { alreadyProcessed: false };
  },
});

export const markEventProcessed = internalMutation({
  args: {
    stripeEventId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const event = await ctx.db
      .query("stripeWebhookEvents")
      .withIndex("by_stripe_event_id", (q) =>
        q.eq("stripeEventId", args.stripeEventId)
      )
      .unique();

    if (event) {
      await ctx.db.patch(event._id, {
        processed: true,
        processedAt: Date.now(),
      });
    }
    return null;
  },
});

export const debugUserUsage = internalQuery({
  args: {
    userId: v.id("users"),
  },
  returns: v.union(
    v.object({
      _id: v.id("userUsage"),
      _creationTime: v.number(),
      userId: v.id("users"),
      plan: v.union(
        v.literal("free"),
        v.literal("pro"),
        v.literal("ultra"),
        v.literal("max")
      ),
      creditsUsed: v.number(),
      creditsLimit: v.number(),
      maxSpendingDollars: v.number(),
      dollarsSpent: v.number(),
      searchesUsed: v.number(),
      resetDate: v.number(),
      stripeCustomerId: v.optional(v.string()),
      stripeSubscriptionId: v.optional(v.string()),
      subscriptionStatus: v.optional(v.string()),
      currentPeriodStart: v.optional(v.number()),
      currentPeriodEnd: v.optional(v.number()),
      cancelAtPeriodEnd: v.optional(v.boolean()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    return usage;
  },
});

export const fixNaNDates = internalMutation({
  args: {
    userId: v.id("users"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();

    if (!usage) {
      return null;
    }

    const updates: any = {};
    let needsUpdate = false;

    // Check and fix resetDate
    if (isNaN(usage.resetDate) || usage.resetDate <= 0) {
      console.log("Fixing invalid resetDate:", usage.resetDate);
      updates.resetDate = Date.now() + 30 * 24 * 60 * 60 * 1000; // 30 days from now
      needsUpdate = true;
    }

    // Check and fix currentPeriodStart
    if (
      usage.currentPeriodStart &&
      (isNaN(usage.currentPeriodStart) || usage.currentPeriodStart <= 0)
    ) {
      console.log(
        "Fixing invalid currentPeriodStart:",
        usage.currentPeriodStart
      );
      updates.currentPeriodStart = Date.now(); // Current time
      needsUpdate = true;
    }

    // Check and fix currentPeriodEnd
    if (
      usage.currentPeriodEnd &&
      (isNaN(usage.currentPeriodEnd) || usage.currentPeriodEnd <= 0)
    ) {
      console.log("Fixing invalid currentPeriodEnd:", usage.currentPeriodEnd);
      updates.currentPeriodEnd = Date.now() + 30 * 24 * 60 * 60 * 1000; // 30 days from now
      updates.resetDate = updates.currentPeriodEnd; // Sync resetDate with currentPeriodEnd
      needsUpdate = true;
    }

    if (needsUpdate) {
      console.log("Applying fixes:", updates);
      await ctx.db.patch(usage._id, updates);
    }

    return null;
  },
});
