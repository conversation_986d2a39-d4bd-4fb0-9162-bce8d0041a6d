"use node";

import { v } from "convex/values";
import { action, internalAction } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import Stripe from "stripe";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";

// Initialize Stripe lazily to avoid issues during deployment
function getStripe() {
  return new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: "2025-07-30.basil",
  });
}

// Stripe Price IDs for each plan (you'll need to create these in Stripe Dashboard)
export const STRIPE_PLANS = {
  pro: {
    priceId: process.env.STRIPE_PRO_PRICE_ID!,
    credits: 500,
    searches: 100,
    maxSpendingDollars: 8.0,
  },
  ultra: {
    priceId: process.env.STRIPE_ULTRA_PRICE_ID!,
    credits: 2500,
    searches: 1000,
    maxSpendingDollars: 20.0,
  },
  max: {
    priceId: process.env.STRIPE_MAX_PRICE_ID!,
    credits: 20000,
    searches: 5000,
    maxSpendingDollars: 120.0,
  },
} as const;

// Get or create Stripe customer
export const getOrCreateStripeCustomer = internalAction({
  args: {
    userId: v.id("users"),
    email: v.optional(v.string()),
    name: v.optional(v.string()),
  },
  returns: v.string(),
  handler: async (ctx, args): Promise<string> => {
    // Check if user already has a Stripe customer ID
    const usage = await ctx.runQuery(internal.stripeHelpers.getUserUsage, {
      userId: args.userId,
    });

    if (usage?.stripeCustomerId) {
      return usage.stripeCustomerId;
    }

    // Get user details
    const user = await ctx.runQuery(internal.stripeHelpers.getUserDetails, {
      userId: args.userId,
    });

    // Create new Stripe customer
    const stripe = getStripe();
    const customer = await stripe.customers.create({
      email: args.email || user?.email || undefined,
      name: args.name || user?.name || undefined,
      metadata: {
        userId: args.userId,
      },
    });

    // Update usage record with Stripe customer ID
    await ctx.runMutation(internal.stripeHelpers.updateStripeCustomerId, {
      userId: args.userId,
      stripeCustomerId: customer.id,
    });

    return customer.id;
  },
});

// Create checkout session for subscription
export const createCheckoutSession = action({
  args: {
    plan: v.union(v.literal("pro"), v.literal("ultra"), v.literal("max")),
    successUrl: v.string(),
    cancelUrl: v.string(),
  },
  returns: v.object({
    sessionId: v.string(),
    url: v.string(),
  }),
  handler: async (ctx, args): Promise<{ sessionId: string; url: string }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get or create Stripe customer
    const customerId: string = await ctx.runAction(
      internal.stripe.getOrCreateStripeCustomer,
      {
        userId,
      }
    );

    const planConfig = STRIPE_PLANS[args.plan];
    if (!planConfig) {
      throw new Error("Invalid plan");
    }

    // Create checkout session
    const stripe = getStripe();
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [
        {
          price: planConfig.priceId,
          quantity: 1,
        },
      ],
      success_url: args.successUrl,
      cancel_url: args.cancelUrl,
      metadata: {
        userId,
        plan: args.plan,
      },
      subscription_data: {
        metadata: {
          userId,
          plan: args.plan,
        },
      },
    });

    if (!session.url) {
      throw new Error("Failed to create checkout session");
    }

    return {
      sessionId: session.id,
      url: session.url,
    };
  },
});

// Cancel subscription
export const cancelSubscription = action({
  args: {
    immediate: v.optional(v.boolean()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ctx.runQuery(internal.stripeHelpers.getUserUsage, {
      userId,
    });

    if (!usage?.stripeSubscriptionId) {
      throw new Error("No active subscription found");
    }

    const stripe = getStripe();
    if (args.immediate) {
      // Cancel immediately
      await stripe.subscriptions.cancel(usage.stripeSubscriptionId);
    } else {
      // Cancel at period end
      await stripe.subscriptions.update(usage.stripeSubscriptionId, {
        cancel_at_period_end: true,
      });
    }

    return null;
  },
});

// Create customer portal session
export const createPortalSession = action({
  args: {
    returnUrl: v.string(),
  },
  returns: v.object({
    url: v.string(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ctx.runQuery(internal.stripeHelpers.getUserUsage, {
      userId,
    });

    if (!usage?.stripeCustomerId) {
      throw new Error("No Stripe customer found");
    }

    const stripe = getStripe();
    const session = await stripe.billingPortal.sessions.create({
      customer: usage.stripeCustomerId,
      return_url: args.returnUrl,
    });

    return {
      url: session.url,
    };
  },
});

// Refresh subscription data from Stripe (for debugging/fixing issues)
export const refreshSubscriptionData = action({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ctx.runQuery(internal.stripeHelpers.getUserUsage, {
      userId,
    });

    if (!usage?.stripeSubscriptionId) {
      throw new Error("No subscription found");
    }

    // Fetch fresh data from Stripe with expanded details
    const stripe = getStripe();
    const subscription = await stripe.subscriptions.retrieve(
      usage.stripeSubscriptionId,
      {
        expand: ["latest_invoice", "default_payment_method"],
      }
    );

    console.log(
      "Full subscription object from Stripe:",
      JSON.stringify(subscription, null, 2)
    );

    // Access the billing period properties correctly
    const currentPeriodStart = (subscription as any).current_period_start;
    const currentPeriodEnd = (subscription as any).current_period_end;
    const cancelAtPeriodEnd = (subscription as any).cancel_at_period_end;

    console.log("Extracted billing period data:", {
      id: subscription.id,
      status: subscription.status,
      current_period_start: currentPeriodStart,
      current_period_end: currentPeriodEnd,
      cancel_at_period_end: cancelAtPeriodEnd,
      plan: subscription.metadata?.plan,
      created: subscription.created,
      billing_cycle_anchor: subscription.billing_cycle_anchor,
    });

    // Validate that we have valid period dates
    if (!currentPeriodStart || !currentPeriodEnd) {
      console.error("Invalid billing period from Stripe:", {
        currentPeriodStart,
        currentPeriodEnd,
      });

      // Calculate billing period manually as fallback
      const now = Math.floor(Date.now() / 1000); // Current time in seconds
      const thirtyDaysInSeconds = 30 * 24 * 60 * 60;

      const calculatedPeriodStart = subscription.created || now;
      const calculatedPeriodEnd = calculatedPeriodStart + thirtyDaysInSeconds;

      console.log("Using calculated billing period:", {
        calculatedPeriodStart,
        calculatedPeriodEnd,
      });

      // Update with calculated values
      await ctx.runMutation(internal.stripeHelpers.updateSubscription, {
        stripeCustomerId: subscription.customer as string,
        stripeSubscriptionId: subscription.id,
        plan: (subscription.metadata?.plan as "pro" | "ultra" | "max") || "pro",
        status: subscription.status,
        currentPeriodStart: calculatedPeriodStart * 1000,
        currentPeriodEnd: calculatedPeriodEnd * 1000,
        cancelAtPeriodEnd: cancelAtPeriodEnd || false,
      });

      return null;
    }

    const plan = subscription.metadata?.plan as "pro" | "ultra" | "max";
    if (!plan || !["pro", "ultra", "max"].includes(plan)) {
      throw new Error(`Invalid plan in subscription metadata: ${plan}`);
    }

    // Update with fresh data from Stripe (convert seconds to milliseconds)
    await ctx.runMutation(internal.stripeHelpers.updateSubscription, {
      stripeCustomerId: subscription.customer as string,
      stripeSubscriptionId: subscription.id,
      plan,
      status: subscription.status,
      currentPeriodStart: currentPeriodStart * 1000,
      currentPeriodEnd: currentPeriodEnd * 1000,
      cancelAtPeriodEnd: cancelAtPeriodEnd || false,
    });

    console.log("Successfully updated subscription with periods:", {
      currentPeriodStart: currentPeriodStart * 1000,
      currentPeriodEnd: currentPeriodEnd * 1000,
      resetDate: currentPeriodEnd * 1000,
    });

    return null;
  },
});

// Fix any NaN date values and refresh subscription
export const fixSubscriptionDates = action({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    console.log("Fixing NaN dates for user:", userId);

    // First fix any NaN values
    await ctx.runMutation(internal.stripeHelpers.fixNaNDates, {
      userId,
    });

    // Then refresh subscription data from Stripe
    try {
      // Call the refresh function directly since we're already in an action
      const usage = await ctx.runQuery(internal.stripeHelpers.getUserUsage, {
        userId,
      });

      if (usage?.stripeSubscriptionId) {
        const stripe = getStripe();
        const subscription = await stripe.subscriptions.retrieve(
          usage.stripeSubscriptionId,
          {
            expand: ["latest_invoice", "default_payment_method"],
          }
        );

        const currentPeriodStart = (subscription as any).current_period_start;
        const currentPeriodEnd = (subscription as any).current_period_end;

        if (currentPeriodStart && currentPeriodEnd) {
          await ctx.runMutation(internal.stripeHelpers.updateSubscription, {
            stripeCustomerId: subscription.customer as string,
            stripeSubscriptionId: subscription.id,
            plan:
              (subscription.metadata?.plan as "pro" | "ultra" | "max") || "pro",
            status: subscription.status,
            currentPeriodStart: currentPeriodStart * 1000,
            currentPeriodEnd: currentPeriodEnd * 1000,
            cancelAtPeriodEnd:
              (subscription as any).cancel_at_period_end || false,
          });
        }
      }

      console.log("Successfully fixed and refreshed subscription data");
    } catch (error) {
      console.log(
        "No subscription to refresh, dates have been fixed with fallback values"
      );
    }

    return null;
  },
});
