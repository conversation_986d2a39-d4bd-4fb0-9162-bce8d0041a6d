import { useRef, useEffect, useCallback } from 'react';

interface UseOptimizedTimerOptions {
  startTime: number | null;
  isActive?: boolean;
  formatFunction?: (startTime: number, currentTime: number) => string;
}

interface UseOptimizedTimerReturn {
  timerRef: React.RefObject<HTMLElement>;
  updateTimer: () => void;
}

/**
 * A hook that manages a timer display without causing React rerenders.
 * Updates the DOM directly using requestAnimationFrame for optimal performance.
 */
export const useOptimizedTimer = ({
  startTime,
  isActive = true,
  formatFunction
}: UseOptimizedTimerOptions): UseOptimizedTimerReturn => {
  const timerRef = useRef<HTMLElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const lastUpdateRef = useRef<number>(0);

  // Default format function that matches the existing formatDuration logic
  const defaultFormatFunction = useCallback((start: number, current: number) => {
    const diffMs = current - start;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes % 60}m ${diffSeconds % 60}s`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}m ${diffSeconds % 60}s`;
    } else {
      return `${diffSeconds}s`;
    }
  }, []);

  const formatter = formatFunction || defaultFormatFunction;

  // Function to update the timer display
  const updateTimer = useCallback(() => {
    if (!timerRef.current || !startTime || !isActive) return;

    const currentTime = Date.now();
    
    // Only update if at least 1 second has passed to avoid excessive DOM updates
    if (currentTime - lastUpdateRef.current >= 1000) {
      const formattedTime = formatter(startTime, currentTime);
      timerRef.current.textContent = formattedTime;
      lastUpdateRef.current = currentTime;
    }
  }, [startTime, isActive, formatter]);

  // Animation loop using requestAnimationFrame
  const startAnimationLoop = useCallback(() => {
    const animate = () => {
      updateTimer();
      
      if (startTime && isActive) {
        animationFrameRef.current = requestAnimationFrame(animate);
      }
    };
    
    animate();
  }, [updateTimer, startTime, isActive]);

  // Stop the animation loop
  const stopAnimationLoop = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
  }, []);

  // Effect to manage the timer lifecycle
  useEffect(() => {
    if (startTime && isActive) {
      // Initialize the display immediately
      updateTimer();
      // Start the animation loop
      startAnimationLoop();
    } else {
      // Stop the animation loop when inactive
      stopAnimationLoop();
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      stopAnimationLoop();
    };
  }, [startTime, isActive, startAnimationLoop, stopAnimationLoop, updateTimer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAnimationLoop();
    };
  }, [stopAnimationLoop]);

  return {
    timerRef,
    updateTimer
  };
};

export default useOptimizedTimer;