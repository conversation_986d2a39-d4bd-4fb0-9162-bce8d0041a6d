import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { 
  Edit, Eye, Play, Copy, Download, FileText, Code2, Bar<PERSON>hart2, History, Save,
  Maximize2, Minimize2
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { cn } from '@/lib/utils';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import remarkBreaks from 'remark-breaks';
import remarkEmoji from 'remark-emoji';
import rehypeKatex from 'rehype-katex';
import { toast } from "sonner";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useTheme } from "next-themes";

interface CanvasData {
  type: "markdown" | "code" | "chart" | "react";
  title: string;
  content: string;
  language?: string;
  chartSpec?: string;
  library?: "chartjs" | "echarts" | "d3";
  updatedAt: number;
}

interface CanvasProps {
  messageId: Id<"messages">;
  canvasData: CanvasData;
  isEditable?: boolean;
  onUpdate?: (data: CanvasData) => void;
  isOutdated?: boolean;
}

export const Canvas: React.FC<CanvasProps> = ({ 
  messageId, 
  canvasData, 
  isEditable = true,
  onUpdate,
  isOutdated = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(canvasData.content);
  const [editedTitle, setEditedTitle] = useState(canvasData.title);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<"editor" | "preview">("preview");
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { theme } = useTheme();
  
  const updateMessage = useMutation(api.messages.updateCanvas);

  useEffect(() => {
    setEditedContent(canvasData.content);
    setEditedTitle(canvasData.title);
  }, [canvasData.content, canvasData.title]);

  useEffect(() => {
    if (isOutdated) {
      setIsEditing(false);
    }
  }, [isOutdated]);

  const handleSave = useCallback(async () => {
    try {
      const updatedData: CanvasData = {
        ...canvasData,
        title: editedTitle.trim() || canvasData.title,
        content: editedContent,
        updatedAt: Date.now(),
      };
      await updateMessage({ messageId, canvasData: updatedData as any });
      onUpdate?.(updatedData);
      setIsEditing(false);
      toast.success("Artifact saved");
    } catch (err) {
      console.error("Failed to update canvas:", err);
      toast.error("Failed to save artifact");
    }
  }, [messageId, canvasData, editedTitle, editedContent, updateMessage, onUpdate]);

  const handleCancel = useCallback(() => {
    setEditedContent(canvasData.content);
    setEditedTitle(canvasData.title);
    setIsEditing(false);
  }, [canvasData.content, canvasData.title]);

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(canvasData.content);
      toast.success("Copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy to clipboard");
    }
  }, [canvasData.content]);

  const handleDownload = useCallback(() => {
    const extension = canvasData.type === "markdown" ? "md" : 
                     canvasData.type === "react" ? "tsx" :
                     canvasData.language || "html";
    const filename = `${canvasData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.${extension}`;
    const blob = new Blob([canvasData.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("File downloaded");
  }, [canvasData]);

  const codePreview = useMemo(() => {
    if (isOutdated && !isEditing) return "";
    
    const isDark = theme === 'dark';
    
    // Create self-contained HTML for previewing with theme support
    const getHtml = (bodyContent: string, headContent: string = "") => `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${editedTitle}</title>
          <style>
            body { 
              font-family: system-ui, -apple-system, sans-serif; 
              margin: 0; 
              line-height: 1.6; 
              background-color: ${isDark ? '#0a0a0a' : '#ffffff'};
              color: ${isDark ? '#fafafa' : '#0a0a0a'};
              padding: 20px;
            }
            * {
              color: inherit;
            }
            a {
              color: ${isDark ? '#60a5fa' : '#2563eb'};
            }
            code {
              background-color: ${isDark ? '#262626' : '#f5f5f5'};
              padding: 2px 4px;
              border-radius: 4px;
              font-family: ui-monospace, 'Cascadia Code', monospace;
            }
            pre {
              background-color: ${isDark ? '#171717' : '#f8f9fa'};
              padding: 16px;
              border-radius: 8px;
              overflow-x: auto;
            }
            blockquote {
              border-left: 4px solid ${isDark ? '#374151' : '#d1d5db'};
              padding-left: 16px;
              margin: 16px 0;
              color: ${isDark ? '#9ca3af' : '#6b7280'};
            }
          </style>
          ${headContent}
      </head>
      <body>
          ${bodyContent}
      </body>
      </html>`;

    // Handle React Previews
    if (canvasData.type === "react") {
      const head = `
        <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
        <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
        <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>`;
      const body = `
        <div id="root"></div>
        <script type="text/babel" data-presets="env,react,typescript">
          ${editedContent}
          // --- Render logic ---
          // Find the component to render, falling back to window.App or the last defined component.
          let ComponentToRender = window.App;
          if (typeof App !== 'undefined') {
            ComponentToRender = App;
          } else {
            // Fallback for when 'App' isn't explicitly defined on window
            const exports = {};
            const allCode = new Function('exports', editedContent);
            allCode(exports);
            if(exports.default) ComponentToRender = exports.default;
          }
          if (ComponentToRender) {
            const root = ReactDOM.createRoot(document.getElementById('root'));
            root.render(React.createElement(ComponentToRender));
          } else {
            document.getElementById('root').innerHTML = '<div style="padding:20px; text-align:center; color:${isDark ? '#ef4444' : '#dc2626'};">Component named "App" not found. The AI must define a React component named App.</div>';
          }
        </script>`;
      return getHtml(body, head);
    }
    
    // Handle Chart.js previews (if chartSpec is provided)
    if (canvasData.type === "chart" && canvasData.chartSpec) {
        const head = `<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>`;
        const body = `
          <div style="width: 100%; height: calc(100vh - 40px); display: flex; align-items: center; justify-content: center;">
            <canvas id="myChart"></canvas>
          </div>
          <script>
            try {
              const ctx = document.getElementById('myChart').getContext('2d');
              const spec = ${canvasData.chartSpec};
              // Apply theme-aware colors to chart
              if (${isDark}) {
                if (spec.options) {
                  if (!spec.options.plugins) spec.options.plugins = {};
                  if (!spec.options.plugins.legend) spec.options.plugins.legend = {};
                  if (!spec.options.plugins.legend.labels) spec.options.plugins.legend.labels = {};
                  spec.options.plugins.legend.labels.color = '#fafafa';
                  
                  if (!spec.options.scales) spec.options.scales = {};
                  ['x', 'y'].forEach(axis => {
                    if (!spec.options.scales[axis]) spec.options.scales[axis] = {};
                    if (!spec.options.scales[axis].ticks) spec.options.scales[axis].ticks = {};
                    spec.options.scales[axis].ticks.color = '#fafafa';
                    if (!spec.options.scales[axis].grid) spec.options.scales[axis].grid = {};
                    spec.options.scales[axis].grid.color = '#374151';
                  });
                }
              }
              new Chart(ctx, spec);
            } catch (e) {
              document.body.innerHTML = '<div style="padding:20px; color:${isDark ? '#ef4444' : '#dc2626'};"><strong>Error rendering chart:</strong><br/>' + e.message + '</div>';
            }
          </script>`;
        return getHtml(body, head);
    }

    // Handle standard code previews
    const htmlContent = editedContent;
    if (!htmlContent.toLowerCase().includes('<html')) {
        return getHtml(editedContent);
    }
    return htmlContent;
  }, [canvasData, editedContent, editedTitle, isOutdated, isEditing, theme]);

  const refreshPreview = useCallback(() => {
    if (isOutdated && !isEditing) return;
    if (iframeRef.current && canvasData.type !== "markdown") {
      iframeRef.current.srcdoc = codePreview;
    }
  }, [codePreview, canvasData.type, isOutdated, isEditing]);

  useEffect(() => {
    refreshPreview();
  }, [refreshPreview, isExpanded]);

  const getCanvasIcon = () => {
    if (canvasData.type === "markdown") return <FileText className="h-4 w-4" />;
    if (canvasData.type === "code") return <Code2 className="h-4 w-4" />;
    if (canvasData.type === "react") return <Code2 className="h-4 w-4 text-sky-500" />;
    return <BarChart2 className="h-4 w-4" />;
  };

  const getBadgeText = () => {
    if (isOutdated) return "Outdated";
    if (canvasData.type === "markdown") return "Markdown";
    if (canvasData.type === "chart") return canvasData.library ? canvasData.library.toUpperCase() : "Chart";
    if (canvasData.type === "react") return "React";
    return canvasData.language || "HTML";
  };

  const CanvasInline = () => (
    <div 
      className={cn(
        "group mt-1 flex w-full max-w-lg cursor-pointer items-center justify-between rounded-lg border bg-card p-3 transition-all hover:border-primary/40 hover:shadow-md",
        isOutdated && "border-dashed opacity-70"
      )}
      onClick={() => setIsExpanded(true)}
    >
      <div className="flex items-center gap-3 overflow-hidden">
        <div className="flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-md bg-muted">
          {getCanvasIcon()}
        </div>
        <div className="overflow-hidden">
          <h4 className="truncate text-label-lg leading-tight">{canvasData.title}</h4>
          <p className="truncate text-xs text-muted-foreground">{getBadgeText()}</p>
        </div>
      </div>
      <Maximize2 size={16} className="ml-2 flex-shrink-0 text-muted-foreground transition-transform group-hover:scale-110" />
    </div>
  );

  const CanvasContent = () => (
    <div className="mt-2 flex w-full max-w-4xl flex-col rounded-xl border bg-card shadow-lg transition-all">
      {/* Header */}
      <div className="flex items-center justify-between gap-4 border-b p-3">
        <div className="flex min-w-0 flex-1 items-center gap-3">
          <div className="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-lg bg-muted">
            {React.cloneElement(getCanvasIcon(), { className: "h-5 w-5" })}
          </div>
          <div className="min-w-0 flex-1">
            {isEditing ? (
              <input type="text" value={editedTitle} onChange={(e) => setEditedTitle(e.target.value)} className="w-full bg-transparent text-base font-semibold leading-tight focus:outline-none" />
            ) : (
              <h3 className="truncate text-headline-sm leading-tight">{canvasData.title}</h3>
            )}
          </div>
        </div>
        <div className="flex items-center gap-1">
          {isEditable && (
            isEditing ? (
              <>
                <Button variant="ghost" size="sm" onClick={handleCancel}>Cancel</Button>
                <Button size="sm" onClick={() => void handleSave()}>
                  <Save size={14} className="mr-2" /> Save
                </Button>
              </>
            ) : (
              <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                <Edit size={14} className="mr-2" /> Edit
              </Button>
            )
          )}
          <Separator orientation="vertical" className="mx-1 h-6" />
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => void handleCopy()}><Copy size={14} /></Button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleDownload}><Download size={14} /></Button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsExpanded(false)}><Minimize2 size={16} /></Button>
        </div>
      </div>

      {/* Editor/Preview Area */}
      <div className="h-[500px] overflow-hidden">
        {canvasData.type === 'markdown' ? (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "editor" | "preview")} className="flex h-full flex-col">
            <TabsList className="mx-3 mt-3 flex-shrink-0">
              <TabsTrigger value="preview"><Eye className="mr-2 h-4 w-4" />Preview</TabsTrigger>
              {(isEditing || isEditable) && <TabsTrigger value="editor"><Edit className="mr-2 h-4 w-4" />Edit</TabsTrigger>}
            </TabsList>
            <TabsContent value="preview" className="m-3 mt-2 flex-1 overflow-hidden">
              <div className="prose prose-sm dark:prose-invert h-full max-w-none overflow-auto rounded-lg border bg-card text-card-foreground p-4 shadow-inner">
                <ReactMarkdown remarkPlugins={[remarkMath, remarkGfm, remarkBreaks, remarkEmoji]} rehypePlugins={[[rehypeKatex, { strict: false }]]}>{isEditing ? editedContent : canvasData.content}</ReactMarkdown>
              </div>
            </TabsContent>
            {(isEditing || isEditable) && (
              <TabsContent value="editor" className="m-3 mt-2 h-full">
                <Textarea value={editedContent} onChange={(e) => setEditedContent(e.target.value)} className="h-full resize-none font-mono text-xs bg-background text-foreground" placeholder="Write your markdown..." disabled={!isEditing} />
              </TabsContent>
            )}
          </Tabs>
        ) : (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "editor" | "preview")} className="flex h-full flex-col">
            <TabsList className="mx-3 mt-3 flex-shrink-0">
              <TabsTrigger value="preview"><Play className="mr-2 h-4 w-4" />Preview</TabsTrigger>
              {(isEditing || isEditable) && <TabsTrigger value="editor">{canvasData.type === "code" || canvasData.type === "react" ? <><Code2 className="mr-2 h-4 w-4" /> Code</> : <><BarChart2 className="mr-2 h-4 w-4" /> Chart</>}</TabsTrigger>}
            </TabsList>
            <TabsContent value="preview" className="m-3 mt-2 flex-1 overflow-hidden">
              {isOutdated && !isEditing && !codePreview ? (
                <div className="flex h-full w-full items-center justify-center rounded-lg border bg-muted/20"><div className="space-y-3 text-center"><History className="mx-auto h-8 w-8 text-muted-foreground" /><div className="text-sm text-muted-foreground"><p className="font-medium">Outdated Canvas</p><p>Preview disabled for performance</p></div><Button variant="outline" size="sm" onClick={() => setIsEditing(true)} className="text-xs">Edit to Preview</Button></div></div>
              ) : (
                <iframe ref={iframeRef} srcDoc={codePreview} className="h-full w-full rounded-lg border bg-card shadow-inner" sandbox="allow-scripts allow-same-origin allow-forms" title={`Preview: ${canvasData.title}`} />
              )}
            </TabsContent>
            {(isEditing || isEditable) && (
              <TabsContent value="editor" className="m-3 mt-2 h-full">
                <Textarea value={editedContent} onChange={(e) => setEditedContent(e.target.value)} className="h-full resize-none font-mono text-xs bg-background text-foreground" placeholder="Write your code here..." disabled={!isEditing} />
              </TabsContent>
            )}
          </Tabs>
        )}
      </div>
    </div>
  );
  
  return isExpanded ? <CanvasContent /> : <CanvasInline />;
};

export default Canvas; 