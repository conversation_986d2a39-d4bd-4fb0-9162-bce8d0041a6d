import { useState, useEffect } from "react";
import { useMutation, useQuery, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { Lock, Trash2, User, Mail, Shield, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  SettingsCard,
  SettingsCardContent,
  SettingsCardDescription,
  SettingsCardHeader,
  SettingsCardTitle,
} from "@/components/settings/SettingsCard";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export function AccountSection() {
  const user = useQuery(api.auth.loggedInUser);
  const hasOAuthAuth = useQuery(api.auth.hasOAuthAuth);
  const updateProfile = useMutation(api.userAccount.updateProfile);
  const deleteAccount = useAction(api.userAccount.deleteAccount);
  const changePassword = useMutation(api.userAccount.changePassword);

  const [name, setName] = useState("");
  const [isConfirmAccountDeleteOpen, setIsConfirmAccountDeleteOpen] = useState(false);
  const [showChangePasswordDialog, setShowChangePasswordDialog] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [accountDeleteConfirmText, setAccountDeleteConfirmText] = useState("");

  // Sync local name state when user data is loaded
  useEffect(() => {
    if (user?.name) {
      setName(user.name);
    }
  }, [user]);

  const handleUpdateUser = async () => {
    if (!user) return;
    try {
      await updateProfile({ name });
      toast.success("Profile updated successfully!");
    } catch (error) {
      toast.error("Failed to update profile.");
      console.error(error);
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await deleteAccount();
      toast.success("Your account is being deleted. You will be redirected to the homepage.");
      // Redirect to homepage after a short delay
      setTimeout(() => {
        window.location.href = "/";
      }, 3000);
    } catch (error) {
      toast.error("Failed to delete account.");
      console.error(error);
    }
  };

  const handleChangePassword = async () => {
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }
    
    setPasswordError(null);
    try {
      await changePassword({ currentPassword, newPassword });
      setShowChangePasswordDialog(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      toast.success("Password changed successfully!");
    } catch (error: any) {
      setPasswordError(error?.message ?? "Failed to change password");
    }
  };

  return (
    <div className="space-y-8">
      {/* Profile Card */}
      <SettingsCard>
        <SettingsCardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <SettingsCardTitle>Profile</SettingsCardTitle>
          <SettingsCardDescription>
            Update your personal information and account details.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="name" className="text-base font-semibold text-zinc-200 flex items-center gap-2">
                <User className="h-4 w-4" />
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Your Name"
                className="bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
              />
            </div>
            <div className="space-y-3">
              <Label htmlFor="email" className="text-base font-semibold text-zinc-200 flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email
              </Label>
              <Input 
                id="email" 
                type="email" 
                value={user?.email ?? ""} 
                readOnly 
                className="bg-zinc-800/30 border-zinc-700/30 text-zinc-400 cursor-not-allowed"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => {
                  void handleUpdateUser();
                }}
                className="bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 hover:border-zinc-600/70"
              >
                Save Changes
              </Button>
              
              <Button
                variant="destructive"
                onClick={() => setIsConfirmAccountDeleteOpen(true)}
                className="bg-gradient-to-r from-red-900/60 to-red-800/40 hover:from-red-800/70 hover:to-red-700/50 border border-red-700/50 hover:border-red-600/70"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Account
              </Button>
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      {/* Security Card */}
      <SettingsCard>
        <SettingsCardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
            <div className="text-xs text-zinc-600 font-mono">02</div>
          </div>
          <SettingsCardTitle>Account Security</SettingsCardTitle>
          <SettingsCardDescription>
            {hasOAuthAuth 
              ? "Your account is secured through OAuth authentication."
              : "Manage your account security and password settings."
            }
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="relative group">
            {/* Subtle glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/5 to-zinc-600/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
            
            {/* Main content */}
            <div className="relative flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 rounded-xl bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 group-hover:border-zinc-700/60 transition-all duration-300">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-gradient-to-br from-zinc-800/60 to-slate-800/40 rounded-lg border border-zinc-700/40">
                  <Shield className="h-5 w-5 text-zinc-300" />
                </div>
                <div>
                  {hasOAuthAuth ? (
                    <>
                      <h4 className="font-medium text-lg text-zinc-200 mb-1">OAuth Authentication</h4>
                      <p className="text-sm text-zinc-400">
                        Your account is secured through GitHub or Google. Manage your security settings through your OAuth provider.
                      </p>
                    </>
                  ) : (
                    <>
                      <h4 className="font-medium text-lg text-zinc-200 mb-1">Change Password</h4>
                      <p className="text-sm text-zinc-400">
                        Update your account password for enhanced security
                      </p>
                    </>
                  )}
                </div>
              </div>
              {!hasOAuthAuth && (
                <Button
                  variant="outline"
                  onClick={() => setShowChangePasswordDialog(true)}
                  className="w-full sm:w-auto bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-700/50 text-zinc-300 hover:border-zinc-600/70 hover:bg-gradient-to-r hover:from-zinc-900/60 hover:to-zinc-800/40 hover:text-white"
                >
                  <Lock className="w-4 h-4 mr-2" />
                  Change Password
                </Button>
              )}
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      {/* Change Password Dialog */}
      <Dialog open={showChangePasswordDialog} onOpenChange={setShowChangePasswordDialog}>
        <DialogContent className="sm:max-w-md bg-gradient-to-br from-zinc-900/95 to-slate-900/90 border border-zinc-700/50 backdrop-blur-lg">
          <DialogHeader>
            <DialogTitle className="text-white">Change Password</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Enter your current password and choose a new password.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword" className="text-zinc-200">Current Password</Label>
              <Input
                id="currentPassword"
                type="password"
                placeholder="Enter current password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="newPassword" className="text-zinc-200">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                placeholder="Enter new password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-zinc-200">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Confirm new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
              />
            </div>
            {passwordError && (
              <p className="text-sm text-red-400 bg-red-900/20 border border-red-700/30 rounded px-3 py-2">{passwordError}</p>
            )}
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setShowChangePasswordDialog(false)}
              className="w-full sm:w-auto bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-700/50 text-zinc-300 hover:border-zinc-600/70 hover:text-white"
            >
              Cancel
            </Button>
            <Button 
              onClick={() => void handleChangePassword()} 
              className="w-full sm:w-auto bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 hover:border-zinc-600/70"
            >
              Change Password
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Account Confirmation Dialog */}
      <Dialog 
        open={isConfirmAccountDeleteOpen} 
        onOpenChange={setIsConfirmAccountDeleteOpen}
      >
        <DialogContent className="sm:max-w-md bg-gradient-to-br from-zinc-900/95 to-slate-900/90 border border-zinc-700/50 backdrop-blur-lg">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              Delete Your Account
            </DialogTitle>
            <DialogDescription className="text-zinc-400">
              This action is permanent and cannot be undone. All your data will be permanently deleted.
            </DialogDescription>
          </DialogHeader>
          
          <Alert variant="destructive" className="bg-red-950/30 border-red-800/50">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Critical Warning</AlertTitle>
            <AlertDescription className="text-sm">
              <p className="mb-2">
                Deleting your account will:
              </p>
              <ul className="list-disc pl-4 space-y-1">
                <li>Delete all your conversations</li>
                <li>Remove all your saved memories</li>
                <li>Cancel any active subscriptions</li>
                <li>Delete all API keys and custom integrations</li>
                <li>Permanently remove all your data</li>
              </ul>
            </AlertDescription>
          </Alert>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deleteConfirm" className="text-zinc-200">
                Type "DELETE MY ACCOUNT" to confirm
              </Label>
              <Input
                id="deleteConfirm"
                type="text"
                placeholder="DELETE MY ACCOUNT"
                value={accountDeleteConfirmText}
                onChange={(e) => setAccountDeleteConfirmText(e.target.value)}
                className="bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
              />
            </div>
          </div>
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setIsConfirmAccountDeleteOpen(false);
                setAccountDeleteConfirmText("");
              }}
              className="w-full sm:w-auto bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-700/50 text-zinc-300 hover:border-zinc-600/70 hover:text-white"
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              disabled={accountDeleteConfirmText !== "DELETE MY ACCOUNT"}
              onClick={() => {
                if (accountDeleteConfirmText === "DELETE MY ACCOUNT") {
                  void handleDeleteAccount();
                }
              }}
              className="w-full sm:w-auto bg-gradient-to-r from-red-900/60 to-red-800/40 hover:from-red-800/70 hover:to-red-700/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Account
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
