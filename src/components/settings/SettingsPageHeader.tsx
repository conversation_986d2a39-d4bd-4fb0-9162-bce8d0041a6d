import { Arrow<PERSON><PERSON><PERSON>, Setting<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { SignOutButton } from "@/SignOutButton";

interface SettingsPageHeaderProps {
  onBack: () => void;
}

export function SettingsPageHeader({ onBack }: SettingsPageHeaderProps) {
  return (
    <header className="sticky top-0 z-50 bg-black/50 backdrop-blur-lg border-b border-white/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-16 flex items-center justify-between">
        <div className="flex items-center gap-4 min-w-0 flex-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="rounded-full h-10 w-10 hover:bg-white/10 text-gray-300 hover:text-white transition-all duration-200 flex-shrink-0 border border-zinc-800/40 hover:border-zinc-700/60"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-3 min-w-0">
            <div className="p-2 bg-gradient-to-br from-zinc-800/60 to-slate-800/40 rounded-xl flex-shrink-0 transition-all duration-200 hover:from-zinc-700/70 hover:to-slate-700/50 border border-zinc-700/40">
              <Settings className="h-6 w-6 text-zinc-300" />
            </div>
            <div className="min-w-0">
              <h1 className="text-xl font-bold text-white truncate">Settings</h1>
              <p className="text-sm text-zinc-400 hidden sm:block">Customize your ErzenAI experience</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3 flex-shrink-0">
          <SignOutButton />
        </div>
      </div>
    </header>
  );
} 