import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import {
  IMAGE_MODELS,
  ImageModelId,
} from "@/lib/models";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  SettingsCard,
  SettingsCardHeader,
  SettingsCardTitle,
  SettingsCardDescription,
  SettingsCardContent,
} from "@/components/settings/SettingsCard";
import { Textarea } from "@/components/ui/textarea";
import { Doc } from "../../../convex/_generated/dataModel";
import { cn } from "@/lib/utils";

type Preferences = Doc<"userPreferences">;

export function AISection() {
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);

  if (!preferences) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="text-center">
          <div className="w-8 h-8 bg-zinc-500/60 rounded-full animate-pulse mx-auto mb-4" />
          <p className="text-zinc-400">Loading AI preferences...</p>
        </div>
      </div>
    );
  }

  const {
    temperature,
    systemPrompt,
    useCustomSystemPrompt,
    imageModel,
  } = preferences;

  const update = (updates: Partial<Preferences>) => {
    void updatePreferences(updates);
  };

  const setTemperature = (value: number[]) =>
    update({ temperature: value[0] });
  const setSystemPrompt = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => update({ systemPrompt: e.target.value });
  const setUseCustomSystemPrompt = (value: boolean) =>
    update({ useCustomSystemPrompt: value });
  const setImageModel = (value: ImageModelId) =>
    update({ imageModel: value });

  return (
    <div className="space-y-8">
      {/* Temperature Control */}
      <SettingsCard>
        <SettingsCardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <SettingsCardTitle>
            Temperature Control
          </SettingsCardTitle>
          <SettingsCardDescription>
            Control the creativity and randomness of AI responses.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label className="text-base font-semibold text-zinc-200">
                Temperature
              </Label>
              <span className="text-sm font-mono bg-zinc-800/50 px-2 py-1 rounded text-zinc-300 border border-zinc-700/50">
                {temperature}
              </span>
            </div>
            <div className="space-y-3">
              <Slider
                value={[temperature]}
                onValueChange={(value) => setTemperature(value)}
                max={2}
                min={0}
                step={0.1}
                className="w-full [&_[role=slider]]:bg-zinc-300 [&_[role=slider]]:border-zinc-400"
              />
              <div className="flex justify-between text-xs text-zinc-500">
                <span>Focused (0.0)</span>
                <span>Balanced (1.0)</span>
                <span>Creative (2.0)</span>
              </div>
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      {/* Image Generation Model */}
      <SettingsCard>
        <SettingsCardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
            <div className="text-xs text-zinc-600 font-mono">02</div>
          </div>
          <SettingsCardTitle>
            Image Generation Model
          </SettingsCardTitle>
          <SettingsCardDescription>
            Choose your preferred model for AI image generation
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="space-y-3">
            {Object.values(IMAGE_MODELS).map((imageModelInfo, index) => {
              const isSelected = imageModel === imageModelInfo.id;

              return (
                <div key={imageModelInfo.id} className="relative group">
                  {/* Hover glow effect */}
                  <div className={`absolute -inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm ${
                    isSelected 
                      ? 'bg-gradient-to-r from-zinc-500/20 via-zinc-400/20 to-zinc-500/20' 
                      : 'bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0'
                  }`} />
                  
                  {/* Main image model card */}
                  <div
                    className={cn(
                      "relative p-4 cursor-pointer transition-all duration-300 rounded-xl border backdrop-blur-sm group-hover:transform group-hover:scale-[1.02]",
                      isSelected
                        ? "bg-gradient-to-r from-zinc-800/80 to-slate-800/60 border-zinc-700/70 text-white shadow-lg"
                        : "bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-800/40 text-zinc-300 hover:border-zinc-700/60 hover:bg-gradient-to-r hover:from-zinc-900/60 hover:to-zinc-800/40"
                    )}
                    onClick={() => void setImageModel(imageModelInfo.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-1.5 h-1.5 rounded-full mt-2 transition-colors duration-300 ${
                        isSelected ? 'bg-zinc-400/80' : 'bg-zinc-600/60 group-hover:bg-zinc-500/80'
                      }`} />
                      <div className="flex-1">
                        <div className="font-medium mb-1">
                          {imageModelInfo.displayName}
                        </div>
                        <div className="text-sm text-zinc-400 mb-3">
                          {imageModelInfo.description}
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-zinc-500">
                            {imageModelInfo.speed} • {imageModelInfo.quality}
                          </div>
                          <div className="text-sm font-medium bg-zinc-800/50 px-2 py-1 rounded border border-zinc-700/50">
                            ${imageModelInfo.pricing.toFixed(2)} per image
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </SettingsCardContent>
      </SettingsCard>

      {/* System Prompt */}
      <SettingsCard>
        <SettingsCardHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
            <div className="text-xs text-zinc-600 font-mono">03</div>
          </div>
          <SettingsCardTitle>
            System Prompt & Personality
          </SettingsCardTitle>
          <SettingsCardDescription>
            Define the AI's personality and behavior. This prompt is
            combined with your user instructions to create a unique
            assistant experience.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40">
            <div className="space-y-1">
              <Label
                htmlFor="useCustomSystemPrompt"
                className="text-base font-semibold text-zinc-200"
              >
                Enable Custom System Prompt
              </Label>
              <p className="text-sm text-zinc-400">
                Use a custom system prompt to define the AI's personality
                and behavior
              </p>
            </div>
            <Switch
              id="useCustomSystemPrompt"
              checked={useCustomSystemPrompt}
              onCheckedChange={(checked) =>
                void setUseCustomSystemPrompt(checked)
              }
              className="[&_[data-state=checked]]:bg-zinc-700 [&_[data-state=unchecked]]:bg-zinc-800"
            />
          </div>

          {useCustomSystemPrompt && (
            <div className="space-y-4">
              <Label
                htmlFor="systemPrompt"
                className="text-base font-semibold text-zinc-200"
              >
                System Prompt
              </Label>
              <div className="relative">
                <Textarea
                  id="systemPrompt"
                  value={systemPrompt ?? ""}
                  onChange={(e) => void setSystemPrompt(e)}
                  placeholder="Enter your custom system prompt..."
                  className="min-h-[150px] resize-none bg-zinc-900/50 border-zinc-700/50 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/70"
                  maxLength={5000}
                />
              </div>
              <div className="flex justify-between text-sm text-zinc-500">
                <span>Define how the AI should behave and respond</span>
                <span className="font-mono">{systemPrompt?.length || 0}/5000</span>
              </div>
            </div>
          )}
        </SettingsCardContent>
      </SettingsCard>
    </div>
  );
}