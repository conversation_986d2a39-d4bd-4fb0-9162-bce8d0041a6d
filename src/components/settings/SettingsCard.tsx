import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"

interface SettingsCardProps extends React.HTMLAttributes<HTMLDivElement> {
  loading?: boolean
  children?: React.ReactNode
}

const SettingsCard = React.forwardRef<HTMLDivElement, SettingsCardProps>(
  ({ className, loading = false, children, ...props }, ref) => (
    <div className="relative group">
      {/* Hover glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
      
      {/* Main card */}
      <Card
        ref={ref}
        className={cn(
          "relative bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-xl transition-all duration-300 hover:border-zinc-700/80 shadow-xl",
          loading && "animate-pulse",
          className
        )}
        {...props}
      >
        {children}
      </Card>
    </div>
  )
)
SettingsCard.displayName = "SettingsCard"

const SettingsCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <CardHeader
    ref={ref}
    className={cn("p-6 pb-4 relative", className)}
    {...props}
  />
))
SettingsCardHeader.displayName = "SettingsCardHeader"

const SettingsCardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <CardTitle
    ref={ref}
    className={cn("text-xl font-semibold leading-none tracking-tight text-white", className)}
    {...props}
  />
))
SettingsCardTitle.displayName = "SettingsCardTitle"

const SettingsCardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <CardDescription
    ref={ref}
    className={cn("text-sm text-zinc-400 mt-2 leading-relaxed", className)}
    {...props}
  />
))
SettingsCardDescription.displayName = "SettingsCardDescription"

const SettingsCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <CardContent
    ref={ref}
    className={cn("p-6 pt-0 space-y-6", className)}
    {...props}
  />
))
SettingsCardContent.displayName = "SettingsCardContent"

const SettingsCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <CardFooter
    ref={ref}
    className={cn("flex items-center justify-between p-6 pt-0 gap-4", className)}
    {...props}
  />
))
SettingsCardFooter.displayName = "SettingsCardFooter"

export { 
  SettingsCard, 
  SettingsCardHeader, 
  SettingsCardTitle, 
  SettingsCardDescription, 
  SettingsCardContent, 
  SettingsCardFooter,
  type SettingsCardProps
}