import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  SettingsCard,
  SettingsCardContent,
  SettingsCardDescription,
  SettingsCardHeader,
  SettingsCardTitle,
} from "@/components/settings/SettingsCard";
import { SettingsSkeleton } from "@/components/settings/SettingsSkeleton";
import { useTheme } from "next-themes";
import { Doc } from "../../../convex/_generated/dataModel";
import { cn } from "@/lib/utils";

const COLOR_THEMES = [
  { name: "Default", value: "default", preview: "bg-gradient-to-r from-blue-500 to-purple-600" },
  { name: "Blue", value: "theme-blue", preview: "bg-gradient-to-r from-blue-600 to-blue-400" },
  { name: "Green", value: "theme-green", preview: "bg-gradient-to-r from-green-600 to-green-400" },
  { name: "Purple", value: "theme-purple", preview: "bg-gradient-to-r from-purple-600 to-purple-400" },
  { name: "Orange", value: "theme-orange", preview: "bg-gradient-to-r from-orange-600 to-orange-400" },
  { name: "Pink", value: "theme-pink", preview: "bg-gradient-to-r from-pink-600 to-pink-400" },
  { name: "Teal", value: "theme-teal", preview: "bg-gradient-to-r from-teal-600 to-teal-400" },
  { name: "Red", value: "theme-red", preview: "bg-gradient-to-r from-red-600 to-red-400" },
  { name: "Indigo", value: "theme-indigo", preview: "bg-gradient-to-r from-indigo-600 to-indigo-400" },
  { name: "Sunset", value: "theme-sunset", preview: "bg-gradient-to-r from-orange-500 to-pink-500" },
  { name: "Ocean", value: "theme-ocean", preview: "bg-gradient-to-r from-cyan-600 to-sky-500" },
  { name: "Forest", value: "theme-forest", preview: "bg-gradient-to-r from-emerald-700 to-lime-600" },
  { name: "Gold", value: "theme-gold", preview: "bg-gradient-to-r from-amber-500 to-yellow-400" },
];

const UI_THEMES = [
  { name: "Default", value: "ui-default", description: "Standard interface" },
  { name: "Rounded", value: "ui-rounded", description: "Soft rounded elements" },
  { name: "Square", value: "ui-square", description: "Sharp square elements" },
  { name: "Glassmorphism", value: "ui-glass", description: "Translucent glass effect" },
];

type Preferences = Doc<"userPreferences">;

export function AppearanceSection() {
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);
  const { theme: _systemTheme, setTheme } = useTheme();

  const [currentColorTheme, setCurrentColorTheme] = useState("default");
  const [currentUITheme, setCurrentUITheme] = useState("ui-default");

  // Load current color theme from document classes
  useEffect(() => {
    const currentColorTheme = COLOR_THEMES.find(t =>
      document.documentElement.classList.contains(t.value)
    )?.value ?? "default";
    setCurrentColorTheme(currentColorTheme);
  }, []);

  // Load current UI theme class
  useEffect(() => {
    const cur = UI_THEMES.find(t => document.documentElement.classList.contains(t.value))?.value ?? "ui-default";
    setCurrentUITheme(cur);
  }, []);

  useEffect(() => {
    if (!preferences) return;

    // Apply stored color theme
    if (preferences.colorTheme) {
      COLOR_THEMES.forEach((t) => document.documentElement.classList.remove(t.value));
      if (preferences.colorTheme !== "default") {
        document.documentElement.classList.add(preferences.colorTheme);
      }
      setCurrentColorTheme(preferences.colorTheme);
    }

    // Apply stored UI theme
    if ((preferences as any).uiTheme) {
      UI_THEMES.forEach((t) => document.documentElement.classList.remove(t.value));
      if ((preferences as any).uiTheme !== "ui-default") {
        document.documentElement.classList.add((preferences as any).uiTheme);
      }
      setCurrentUITheme((preferences as any).uiTheme);
    }
  }, [preferences]);

  if (!preferences) {
    return <SettingsSkeleton variant="complex" />;
  }

  const {
    hideUserInfo,
    showToolOutputs,
    showMessageMetadata,
    showThinking,
    theme,
  } = preferences;

  const update = (updates: Partial<Preferences>) => {
    void updatePreferences(updates);
  };

  const setHideUserInfo = (value: boolean) => {
    void updatePreferences({ hideUserInfo: value });
  };

  const setShowToolOutputs = (value: boolean) => {
    void updatePreferences({ showToolOutputs: value });
  };

  const setShowMessageMetadata = (value: boolean) => {
    void updatePreferences({ showMessageMetadata: value });
  };

  const setShowThinking = (value: boolean) => {
    void updatePreferences({ showThinking: value });
  };

  const setColorTheme = (theme: string) => {
    // Remove all existing color theme classes
    COLOR_THEMES.forEach(t => {
      document.documentElement.classList.remove(t.value);
    });
    
    // Add the new theme class
    if (theme !== "default") {
      document.documentElement.classList.add(theme);
    }
    
    setCurrentColorTheme(theme);
    update({ colorTheme: theme });
  };

  const setUITheme = (theme: string) => {
    // Remove all existing UI theme classes
    UI_THEMES.forEach(t => {
      document.documentElement.classList.remove(t.value);
    });
    
    // Add the new theme class
    if (theme !== "ui-default") {
      document.documentElement.classList.add(theme);
    }
    
    setCurrentUITheme(theme);
    update({ uiTheme: theme });
  };

  return (
    <div className="space-y-6">
      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle>Privacy Settings</SettingsCardTitle>
          <SettingsCardDescription>
            Control what information is displayed in the chat interface.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent className="space-y-4">
          <div className="space-y-6">
            <h4 className="font-semibold text-lg text-primary flex items-center gap-2">
              Privacy Controls
            </h4>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="hideUserInfo">Hide User Info</Label>
                  <p className="text-sm text-muted-foreground">
                    Hide your name and avatar from the chat interface
                  </p>
                </div>
                <Switch
                  id="hideUserInfo"
                  checked={hideUserInfo}
                  onCheckedChange={(checked) => void setHideUserInfo(checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="showToolOutputs">Show Tool Outputs</Label>
                  <p className="text-sm text-muted-foreground">
                    Display detailed outputs from AI tools and functions
                  </p>
                </div>
                <Switch
                  id="showToolOutputs"
                  checked={showToolOutputs}
                  onCheckedChange={(checked) => void setShowToolOutputs(checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="showMessageMetadata">Show Message Metadata</Label>
                  <p className="text-sm text-muted-foreground">
                    Display timestamps and other message details
                  </p>
                </div>
                <Switch
                  id="showMessageMetadata"
                  checked={showMessageMetadata}
                  onCheckedChange={(checked) => void setShowMessageMetadata(checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="showThinking">Show AI Thinking</Label>
                  <p className="text-sm text-muted-foreground">
                    Display the AI's reasoning process before responses
                  </p>
                </div>
                <Switch
                  id="showThinking"
                  checked={showThinking}
                  onCheckedChange={(checked) => void setShowThinking(checked)}
                />
              </div>
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle>Theme Settings</SettingsCardTitle>
          <SettingsCardDescription>
            Customize the appearance and visual style of the interface.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent className="space-y-6">
          <div className="space-y-4">
            <h4 className="font-semibold">System Theme</h4>
            <div className="grid grid-cols-3 gap-3">
              {["light", "dark", "system"].map((themeOption) => (
                <div
                  key={themeOption}
                  className={cn(
                    "rounded-lg border bg-card text-card-foreground p-3 cursor-pointer transition-all duration-200 hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:scale-[1.02] text-center",
                    (theme === themeOption ||
                      (theme === undefined && themeOption === "system")) &&
                      "bg-accent text-accent-foreground shadow-md ring-2 ring-primary/20",
                  )}
                  onClick={() => {
                    setTheme(themeOption);
                    update({ theme: themeOption as "light" | "dark" | "system" });
                  }}
                >
                  <div className="capitalize font-semibold">{themeOption}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold">Color Theme</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
              {COLOR_THEMES.map((t) => (
                <div
                  key={t.value}
                  onClick={() => void setColorTheme(t.value)}
                  className={cn(
                    "flex items-center gap-3 rounded-lg border p-3 text-sm transition-all duration-200 hover:bg-accent hover:shadow-md hover:scale-[1.02] cursor-pointer",
                    currentColorTheme === t.value && "bg-accent shadow-md ring-2 ring-primary/20",
                  )}
                >
                  <div className={cn("h-6 w-6 rounded-full shadow-sm", t.preview)} />
                  <span className="font-medium">{t.name}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold">UI Theme</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {UI_THEMES.map((t) => (
                <div
                  key={t.value}
                  onClick={() => void setUITheme(t.value)}
                  className={cn(
                    "rounded-lg border bg-card text-card-foreground p-4 cursor-pointer transition-all duration-200 hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:scale-[1.02]",
                    currentUITheme === t.value &&
                      "bg-accent text-accent-foreground shadow-md ring-2 ring-primary/20",
                  )}
                >
                  <div className="font-semibold">{t.name}</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {t.description}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>
    </div>
  );
} 
