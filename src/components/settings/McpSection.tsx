import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  SettingsCard,
  SettingsCardContent,
  SettingsCardDescription,
  SettingsCardHeader,
  SettingsCardTitle,
} from "@/components/settings/SettingsCard";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Edit, Wrench, Zap, Globe, Settings } from "lucide-react";
import { Doc } from "../../../convex/_generated/dataModel";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ListSettingsSkeleton } from "@/components/settings/SettingsSkeleton";

type McpServer = Doc<"mcpServers">;

function McpServerForm({
  server,
  onSave,
  onClose,
}: {
  server?: McpServer;
  onSave: (data: Partial<McpServer>) => void;
  onClose: () => void;
}) {
  const [name, setName] = useState(server?.name ?? "");
  const [description, setDescription] = useState(server?.description ?? "");
  // Only SSE or HTTP are now selectable in the UI
  const [transportType, setTransportType] = useState<"sse" | "http">(
    (server as any)?.transportType === "http" ? "http" : "sse"
  );
  const [url, setUrl] = useState(server?.url ?? "");
  const [headers, setHeaders] = useState(
    server && (server as any).headers 
      ? JSON.stringify((server as any).headers, null, 2) 
      : ""
  );

  const handleSubmit = () => {
    try {
      const parsedHeaders = headers.trim() ? JSON.parse(headers) : undefined;
      onSave({ 
        name, 
        description, 
        transportType, 
        url, 
        headers: parsedHeaders 
      });
    } catch (error) {
      toast.error("Invalid JSON in headers field");
    }
  };

  return (
    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto bg-gradient-to-br from-zinc-900/95 via-slate-900/90 to-zinc-900/95 backdrop-blur-xl border border-zinc-800/50">
      <DialogHeader className="pb-6">
        <DialogTitle className="text-2xl font-semibold text-white flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center">
            <Wrench className="h-4 w-4 text-zinc-300" />
          </div>
          {server ? "Edit MCP Server" : "Add MCP Server"}
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-6">
        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-zinc-300">Server Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter server name"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-zinc-300">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Server description (optional)"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors resize-none"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="transport-type" className="text-sm font-medium text-zinc-300">Transport Type *</Label>
            <Select value={transportType} onValueChange={(value: "sse" | "http") => setTransportType(value)}>
              <SelectTrigger className="bg-zinc-900/50 border-zinc-800/60 text-white focus:border-zinc-700">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-zinc-900 border-zinc-800">
                <SelectItem value="sse" className="text-white hover:bg-zinc-800">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-400" />
                    <span>Server-Sent Events (SSE)</span>
                  </div>
                </SelectItem>
                <SelectItem value="http" className="text-white hover:bg-zinc-800">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-green-400" />
                    <span>HTTP</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="url" className="text-sm font-medium text-zinc-300">Server URL *</Label>
            <Input
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com/api"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="headers" className="text-sm font-medium text-zinc-300">Headers (JSON)</Label>
            <Textarea
              id="headers"
              value={headers}
              onChange={(e) => setHeaders(e.target.value)}
              placeholder='{"Authorization": "Bearer token"}'
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors font-mono text-xs"
              rows={4}
            />
          </div>
        </div>
      </div>
      <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-3 pt-6">
        <Button
          variant="outline"
          onClick={onClose}
          className="bg-zinc-900/50 border-zinc-800/60 text-zinc-300 hover:bg-zinc-800/50 hover:text-white transition-all duration-200"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!name.trim() || !url.trim()}
          className="bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 transition-all duration-200 hover:scale-105"
        >
          {server ? "Update" : "Add"} Server
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}

export function McpSection() {
  const mcpServers = useQuery(api.mcpServers.list);
  const addServer = useMutation(api.mcpServers.add);
  const updateServer = useMutation(api.mcpServers.update);
  const removeServer = useMutation(api.mcpServers.remove);
  const updateToolsCache = useMutation(api.mcpServers.updateToolsCache);
  const fetchAvailableTools = useAction(api.ai.getAvailableTools);
  const toggleServer = useMutation(api.mcpServers.toggle);
  const updatePreferences = useMutation(api.preferences.update);
  const preferences = useQuery(api.preferences.get);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingServer, setEditingServer] = useState<McpServer | undefined>();

  // Show loading state while data is being fetched
  if (mcpServers === undefined) {
    return <ListSettingsSkeleton />;
  }

  const handleSave = async (data: Partial<McpServer>) => {
    try {
      let serverId: any;
      if (editingServer) {
        await updateServer({ id: editingServer._id, ...data });
        serverId = editingServer._id;
        toast.success("MCP server updated.");
      } else {
        serverId = await addServer(data as any);
        toast.success("MCP server added.");
      }

      // Refresh available tools and cache them for this server
      try {
        const toolsConfig: Record<string, any> = await fetchAvailableTools({});
        const serverName = (data.name ?? editingServer?.name ?? "").toLowerCase().replace(/[^a-z0-9]/g, "_");
        const prefix = `mcp_${serverName}_`;
        const toolIds = Object.keys(toolsConfig).filter((id) => id.startsWith(prefix));

        if (toolIds.length > 0) {
          await updateToolsCache({ id: serverId, availableTools: toolIds });
        }
      } catch (toolErr) {
        console.error("Failed to update MCP server tools cache:", toolErr);
      }

      // ensure base tool id is in enabledTools list
      const baseToolId = `mcp_${(data.name ?? editingServer?.name ?? "").toLowerCase().replace(/[^a-z0-9]/g, "_")}`;
      if (preferences) {
        const enabled = preferences.enabledTools ?? [];
        if (!enabled.includes(baseToolId)) {
          await updatePreferences({ enabledTools: [...enabled, baseToolId] });
        }
      }
      setIsFormOpen(false);
      setEditingServer(undefined);
    } catch {
      toast.error("Failed to save MCP server.");
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await removeServer({ id });
      toast.success("MCP server removed.");
    } catch {
      toast.error("Failed to remove MCP server.");
    }
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Info Section */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
        <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/40 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-xl p-6 transition-all duration-300 hover:border-zinc-700/80">
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-4 right-4 w-16 h-16 border border-white/20 rounded-full" />
            <div className="absolute bottom-6 left-6 w-12 h-12 border border-white/10 rounded-full" />
          </div>
          <div className="relative z-10 flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50">
              <Wrench className="h-6 w-6 text-zinc-300" />
            </div>
            <div className="flex-1 space-y-2">
              <h3 className="text-lg font-semibold text-white">Model Context Protocol (MCP) Servers</h3>
              <p className="text-zinc-400 leading-relaxed">
                MCP servers provide additional tools and capabilities to your AI assistant. Connect to servers for things like code execution, database access, and more.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Card */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
        <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl transition-all duration-300 hover:border-zinc-700/80">
          {/* Enhanced Header */}
          <div className="p-8 pb-6">
            <div className="flex flex-row items-center justify-between space-y-0">
              <div className="space-y-2">
                <h2 className="text-2xl font-semibold text-white tracking-tight">Your MCP Servers</h2>
                <p className="text-zinc-400">Manage your connected MCP servers and their capabilities.</p>
              </div>
              <Button
                onClick={() => {
                  setEditingServer(undefined);
                  setIsFormOpen(true);
                }}
                className="bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 transition-all duration-200 hover:scale-105 shadow-lg"
              >
                <Plus className="mr-2 h-4 w-4" /> Add Server
              </Button>
            </div>
          </div>

          {/* Enhanced Content */}
          <div className="px-8 pb-8">
            {mcpServers.length === 0 ? (
              <div className="text-center py-16">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-zinc-800/20 to-slate-800/20 rounded-full blur-2xl" />
                  <div className="relative bg-gradient-to-br from-zinc-900/50 to-slate-900/30 rounded-2xl p-12 border border-zinc-800/40">
                    <div className="space-y-6">
                      <div className="w-16 h-16 mx-auto rounded-xl bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50">
                        <Wrench className="h-8 w-8 text-zinc-400" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold text-white">No MCP servers configured yet</h3>
                        <p className="text-zinc-400 max-w-md mx-auto leading-relaxed">
                          Add your first MCP server to get started with additional tools and capabilities for your AI assistant.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {mcpServers.map((server, index) => (
                  <div key={server._id} className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/8 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
                    <div className="relative bg-gradient-to-br from-zinc-900/70 via-slate-900/40 to-zinc-900/70 backdrop-blur-sm border border-zinc-800/50 rounded-xl transition-all duration-300 hover:border-zinc-700/70 hover:transform hover:scale-[1.01] hover:shadow-xl">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5">
                        {index % 3 === 0 && (
                          <>
                            <div className="absolute top-4 right-4 w-12 h-12 border border-white/20 rounded-full" />
                            <div className="absolute bottom-6 left-6 w-8 h-8 border border-white/10 rounded-full" />
                          </>
                        )}
                        {index % 3 === 1 && (
                          <>
                            <div className="absolute top-6 left-4 w-6 h-6 bg-white/10 rounded" />
                            <div className="absolute bottom-4 right-6 w-4 h-4 bg-white/8 rounded" />
                          </>
                        )}
                        {index % 3 === 2 && (
                          <>
                            <div className="absolute top-4 left-4 w-16 h-px bg-white/20" />
                            <div className="absolute top-8 left-4 w-12 h-px bg-white/15" />
                          </>
                        )}
                      </div>

                      {/* Header */}
                      <div className="relative z-10 p-6 pb-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4 flex-1 min-w-0">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50 flex-shrink-0">
                              {server.transportType === 'sse' ? (
                                <Zap className="h-5 w-5 text-blue-400" />
                              ) : (
                                <Globe className="h-5 w-5 text-green-400" />
                              )}
                            </div>
                            <div className="space-y-2 flex-1 min-w-0">
                              <div className="flex items-center gap-3 flex-wrap">
                                <h3 className="text-lg font-semibold text-white truncate">{server.name}</h3>
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="secondary" 
                                    className="text-xs font-medium bg-zinc-800/50 text-zinc-300 border-zinc-700/50"
                                  >
                                    {server.transportType.toUpperCase()}
                                  </Badge>
                                  <Badge 
                                    variant={server.isEnabled ? "default" : "secondary"} 
                                    className={`text-xs font-medium transition-colors duration-200 ${
                                      server.isEnabled 
                                        ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30" 
                                        : "bg-zinc-800/50 text-zinc-400 border-zinc-700/50"
                                    }`}
                                  >
                                    {server.isEnabled ? "Enabled" : "Disabled"}
                                  </Badge>
                                </div>
                              </div>
                              {server.description && (
                                <p className="text-sm text-zinc-400 leading-relaxed">{server.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingServer(server);
                                setIsFormOpen(true);
                              }}
                              className="text-zinc-400 hover:text-white hover:bg-zinc-800/50 transition-all duration-200"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={server.isEnabled ? "destructive" : "default"}
                              size="sm"
                              onClick={() => void toggleServer({ id: server._id })}
                              className={`transition-all duration-200 ${
                                server.isEnabled 
                                  ? "bg-red-500/20 text-red-400 hover:bg-red-500/30 border-red-500/30" 
                                  : "bg-emerald-500/20 text-emerald-400 hover:bg-emerald-500/30 border-emerald-500/30"
                              }`}
                            >
                              {server.isEnabled ? "Disable" : "Enable"}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => void handleDelete(server._id)}
                              className="text-zinc-400 hover:text-red-400 hover:bg-red-500/10 transition-all duration-200"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="relative z-10 px-6 pb-6">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 text-sm">
                            <Globe className="h-4 w-4 text-zinc-500" />
                            <span className="text-zinc-400 font-medium">URL:</span>
                            <code className="text-zinc-300 bg-zinc-800/50 px-2 py-1 rounded text-xs font-mono break-all">{server.url}</code>
                          </div>
                          {server.availableTools && server.availableTools.length > 0 && (
                            <div className="flex items-start gap-2 text-sm">
                              <Settings className="h-4 w-4 text-zinc-500 mt-0.5" />
                              <span className="text-zinc-400 font-medium">Tools:</span>
                              <div className="flex flex-wrap gap-1">
                                {server.availableTools.slice(0, 3).map((tool, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs bg-zinc-800/30 text-zinc-400 border-zinc-700/50">
                                    {tool}
                                  </Badge>
                                ))}
                                {server.availableTools.length > 3 && (
                                  <Badge variant="outline" className="text-xs bg-zinc-800/30 text-zinc-400 border-zinc-700/50">
                                    +{server.availableTools.length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <McpServerForm
          server={editingServer}
          onSave={(data) => void handleSave(data)}
          onClose={() => setIsFormOpen(false)}
        />
      </Dialog>
    </div>
  );
} 
