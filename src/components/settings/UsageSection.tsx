import { useQuery, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UsageSettingsSkeleton } from "@/components/settings/SettingsSkeleton";
import { 
  TrendingUp, 
  Zap, 
  Settings,
  ArrowUp,
  Crown, 
  Activity,
  BarChart3
} from "lucide-react";
import {
  Pie<PERSON>hart as Recharts<PERSON>ie<PERSON>hart,
  ResponsiveContainer,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Area,
  Pie,
  AreaChart,
  Bar<PERSON>hart,
  Bar
} from "recharts";

const PLAN_DETAILS = {
  free: {
    name: "Free",
    price: "Free",
    credits: 100,
    searches: 10,
    icon: "🆓",
    color: "from-zinc-500 to-slate-500",
    description: "Perfect for getting started"
  },
  pro: {
    name: "Pro",
    price: "€15",
    credits: 500,
    searches: 100,
    icon: "⚡",
    color: "from-blue-500 to-cyan-500",
    description: "For regular users"
  },
  ultra: {
    name: "Ultra",
    price: "€40",
    credits: 2500,
    searches: 1000,
    icon: "🚀",
    color: "from-purple-500 to-pink-500",
    description: "For power users"
  },
  max: {
    name: "Max",
    price: "€200",
    credits: 20000,
    searches: 5000,
    icon: "👑",
    color: "from-amber-500 to-orange-500",
    description: "For teams and enterprises"
  },
} as const;

export function UsageSection() {
  const [isLoading, setIsLoading] = useState(false);
  const [analyticsTimeRange, setAnalyticsTimeRange] = useState<number>(30);

  const usage = useQuery(api.usage.get);
  const subscription = useQuery(api.subscriptions.getSubscription);
  const analyticsData = useQuery(api.messages.getUsageAnalytics, { days: analyticsTimeRange });

  const createCheckoutSession = useAction(api.stripe.createCheckoutSession);
  const cancelSubscription = useAction(api.stripe.cancelSubscription);
  const createPortalSession = useAction(api.stripe.createPortalSession);

  if (!usage) {
    return <UsageSettingsSkeleton />;
  }

  const { plan, creditsUsed, creditsLimit, searchesUsed, resetDate } = usage;
  const creditsPercentage = (creditsUsed / creditsLimit) * 100;
  const searchesPercentage = (searchesUsed / PLAN_DETAILS[plan].searches) * 100;
  const currentPlanDetails = PLAN_DETAILS[plan];

  const handleUpgrade = async (targetPlan: "pro" | "ultra" | "max") => {
    try {
      setIsLoading(true);
      const result = await createCheckoutSession({
        plan: targetPlan,
        successUrl: window.location.href,
        cancelUrl: window.location.href,
      });
      window.location.href = result.url;
    } catch (error) {
      console.error("Failed to create checkout session:", error);
      alert("Failed to start checkout process. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (
      !confirm(
        "Are you sure you want to cancel your subscription? You'll be downgraded to the free plan at the end of your billing period."
      )
    ) {
      return;
    }
    try {
      setIsLoading(true);
      await cancelSubscription({ immediate: false });
      alert(
        "Subscription canceled successfully. You'll continue to have access until the end of your billing period."
      );
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
      alert("Failed to cancel subscription. Please try again or contact support.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setIsLoading(true);
      const result = await createPortalSession({
        returnUrl: window.location.href,
      });
      window.location.href = result.url;
    } catch (error) {
      console.error("Failed to create portal session:", error);
      alert("Failed to open billing portal. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };


  const getAvailableUpgrades = () => {
    const plans = Object.keys(PLAN_DETAILS) as Array<keyof typeof PLAN_DETAILS>;
    const currentIndex = plans.indexOf(plan);
    return plans.slice(currentIndex + 1);
  };

  const availableUpgrades = getAvailableUpgrades();

  const formatTimestamp = (ts: number | undefined): string => {
    if (!ts || ts <= 0 || isNaN(ts)) {
      return "Not available";
    }
    let effectiveTs = ts;
    if (ts < 1000000000000) {
      effectiveTs = ts * 1000;
    }
    const d = new Date(effectiveTs);
    if (isNaN(d.getTime())) {
      return "Invalid date";
    }
    
    const now = new Date();
    const diffDays = Math.ceil((d.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return "Today";
    } else if (diffDays === 1) {
      return "Tomorrow";
    } else if (diffDays > 1 && diffDays <= 7) {
      return `In ${diffDays} days (${d.toLocaleDateString()})`;
    } else if (diffDays < 0 && diffDays >= -7) {
      return `${Math.abs(diffDays)} days ago (${d.toLocaleDateString()})`;
    } else {
      return d.toLocaleDateString();
    }
  };

  // Modern chart theme colors
  const chartColors = [
    "#6366f1", "#8b5cf6", "#ec4899", "#10b981", 
    "#f59e0b", "#ef4444", "#06b6d4", "#3b82f6"
  ];

  // Custom tooltip component for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload?.length) {
      return (
        <div className="bg-black/90 backdrop-blur-md border border-zinc-800 rounded-lg px-3 py-2 shadow-xl">
          <p className="text-white text-xs font-medium mb-1">{label}</p>
          {payload.map((entry: any) => (
            <p key={`tooltip-${entry.name}`} className="text-xs flex items-center gap-1.5" style={{ color: entry.color }}>
              <span className="w-2 h-2 rounded-full" style={{ backgroundColor: entry.color }}></span>
              <span className="font-medium">{entry.name}: </span>
              <span>{typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}</span>
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Prepare model data for the horizontal chart
  const prepareModelData = () => {
    if (!analyticsData?.modelBreakdown) return [];
    
    // Get top 5 models by token usage
    return analyticsData.modelBreakdown
      .slice(0, 5)
      .map(model => ({
        name: model.model.length > 15 ? `${model.model.slice(0, 15)}...` : model.model,
        value: model.totalTokens,
        fullName: model.model
      }))
      .sort((a, b) => a.value - b.value); // Sort ascending for horizontal chart
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Summary */}
      <div className="bg-gradient-to-br from-zinc-900/80 to-black/60 backdrop-blur-md border border-zinc-800/50 rounded-xl p-6 overflow-hidden relative">
        <div className="absolute inset-0 bg-grid-white/[0.02] pointer-events-none"></div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <h2 className="text-xl font-semibold text-white tracking-tight flex items-center gap-2">
                <span className="text-2xl">{currentPlanDetails.icon}</span>
                {currentPlanDetails.name} Plan
              </h2>
              {subscription?.hasSubscription && (
                <Badge
                  variant={subscription.status === "active" ? "default" : "secondary"}
                  className={`font-medium ${
                    subscription.status === "active"
                      ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30"
                      : "bg-zinc-800/50 text-zinc-400 border-zinc-700/50"
                  }`}
                >
                  {subscription.status}
                  {subscription.cancelAtPeriodEnd && " (Canceling)"}
                </Badge>
              )}
            </div>
            <p className="text-zinc-400 text-sm">
              {currentPlanDetails.price !== "Free" ? 
                `${currentPlanDetails.price}/month · Resets on ${formatTimestamp(resetDate)}` : 
                `Free plan · Resets on ${formatTimestamp(resetDate)}`}
            </p>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2">
            {subscription?.hasSubscription ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => void handleManageSubscription()}
                  disabled={isLoading}
                  size="sm"
                  className="bg-zinc-900/50 border-zinc-800/60 text-zinc-300 hover:bg-zinc-800/50 hover:text-white transition-all duration-200"
                >
                  <Settings className="h-3.5 w-3.5 mr-1.5" />
                  Manage Billing
                </Button>
                {!subscription.cancelAtPeriodEnd && (
                  <Button
                    variant="destructive"
                    onClick={() => void handleCancelSubscription()}
                    disabled={isLoading}
                    size="sm"
                    className="bg-red-500/10 text-red-400 hover:bg-red-500/20 border-red-500/30 transition-all duration-200"
                  >
                    Cancel
                  </Button>
                )}
              </>
            ) : availableUpgrades.length > 0 && (
              <Button
                size="sm"
                onClick={() => void handleUpgrade("pro")}
                disabled={isLoading}
                className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-500 hover:to-violet-500 text-white border-none transition-all duration-200"
              >
                <ArrowUp className="h-3.5 w-3.5 mr-1.5" />
                Upgrade
              </Button>
            )}
          </div>
        </div>

        {/* Usage Meters */}
        <div className="mt-5 grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Credits Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <Zap className="h-3.5 w-3.5 text-blue-400" />
                <span className="text-xs font-medium text-white">Credits</span>
              </div>
              <span className="text-xs text-zinc-400 font-mono">
                {creditsUsed.toLocaleString()} / {creditsLimit.toLocaleString()}
              </span>
            </div>
            <div className="relative h-2">
              <Progress 
                value={creditsPercentage} 
                className="h-2 rounded-full bg-zinc-800/50"
              />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-500/20 to-violet-500/20 blur-[1px]" />
            </div>
          </div>

          {/* Searches Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <TrendingUp className="h-3.5 w-3.5 text-emerald-400" />
                <span className="text-xs font-medium text-white">Searches</span>
              </div>
              <span className="text-xs text-zinc-400 font-mono">
                {searchesUsed} / {currentPlanDetails.searches}
              </span>
            </div>
            <div className="relative h-2">
              <Progress 
                value={searchesPercentage} 
                className="h-2 rounded-full bg-zinc-800/50"
              />
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-emerald-500/20 to-green-500/20 blur-[1px]" />
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-4.5 w-4.5 text-indigo-400" />
            <h4 className="font-semibold text-white">Analytics</h4>
          </div>
          <Select 
            value={analyticsTimeRange.toString()} 
            onValueChange={(value) => setAnalyticsTimeRange(parseInt(value))}
          >
            <SelectTrigger className="w-28 h-8 text-xs bg-black/60 border-zinc-800 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-black border border-zinc-800 text-xs">
              <SelectItem value="7" className="text-white">7 days</SelectItem>
              <SelectItem value="30" className="text-white">30 days</SelectItem>
              <SelectItem value="90" className="text-white">90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {!analyticsData ? (
          // Loading state
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2].map((i) => (
              <div key={`loading-${i}`} className="bg-zinc-900/30 rounded-xl border border-zinc-800/40 h-[240px] animate-pulse" />
            ))}
          </div>
        ) : analyticsData.totals.totalMessages === 0 ? (
          // No data state
          <div className="text-center py-8 text-zinc-400 bg-black/40 rounded-xl border border-zinc-800/40">
            <BarChart3 className="h-10 w-10 mx-auto mb-3 opacity-40" />
            <p className="text-sm">No usage data available for the selected time period</p>
          </div>
        ) : (
          // Charts
          <div className="space-y-4">
            {/* Main charts row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Daily Usage Chart */}
              <div className="bg-black/60 border border-zinc-800/40 rounded-xl p-4 overflow-hidden">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="text-xs font-medium text-white">Token Usage Over Time</h5>
                </div>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart 
                      data={analyticsData.dailyUsage}
                      margin={{ top: 5, right: 5, left: 0, bottom: 5 }}
                    >
                      <defs>
                        <linearGradient id="colorPrompt" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#6366f1" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#6366f1" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="colorCompletion" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#27272a" vertical={false} />
                      <XAxis 
                        dataKey="date" 
                        stroke="#52525b" 
                        fontSize={10} 
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => {
                          const date = new Date(value);
                          return date.toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric' 
                          });
                        }}
                      />
                      <YAxis 
                        stroke="#52525b" 
                        fontSize={10} 
                        tickLine={false}
                        axisLine={false}
                        width={30}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Area 
                        type="monotone" 
                        dataKey="promptTokens" 
                        name="Prompt" 
                        stroke="#6366f1" 
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorPrompt)" 
                      />
                      <Area 
                        type="monotone" 
                        dataKey="completionTokens" 
                        name="Completion" 
                        stroke="#8b5cf6" 
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorCompletion)" 
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Model Distribution - Horizontal Bar Chart */}
              <div className="bg-black/60 border border-zinc-800/40 rounded-xl p-4 overflow-hidden">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="text-xs font-medium text-white">Top Models</h5>
                </div>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={prepareModelData()}
                      layout="vertical"
                      margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#27272a" horizontal={false} />
                      <XAxis 
                        type="number" 
                        stroke="#52525b" 
                        fontSize={10} 
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => value.toLocaleString()}
                      />
                      <YAxis 
                        type="category" 
                        dataKey="name" 
                        width={80}
                        stroke="#52525b" 
                        fontSize={10} 
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip 
                        content={({ active, payload }) => {
                          if (active && payload?.length) {
                            const data = payload[0].payload;
                            return (
                              <div className="bg-black/90 backdrop-blur-md border border-zinc-800 rounded-lg px-3 py-2 shadow-xl">
                                <p className="text-white text-xs font-medium">{data.fullName}</p>
                                <p className="text-xs text-zinc-300">
                                  <span className="font-medium">Tokens: </span>
                                  <span>{data.value.toLocaleString()}</span>
                                </p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Bar 
                        dataKey="value" 
                        fill="#8b5cf6" 
                        radius={[0, 4, 4, 0]}
                        barSize={18}
                      >
                        {prepareModelData().map((entry, index) => (
                          <Cell 
                            key={`cell-${entry.name}`} 
                            fill={chartColors[index % chartColors.length]} 
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Summary Stats */}
            <div className="bg-black/60 border border-zinc-800/40 rounded-xl p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-1">
                  <div className="text-xs text-zinc-500">Total Tokens</div>
                  <div className="text-xl font-semibold text-white">{analyticsData.totals.totalTokens.toLocaleString()}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-zinc-500">Messages</div>
                  <div className="text-xl font-semibold text-white">{analyticsData.totals.totalMessages.toLocaleString()}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-zinc-500">Input</div>
                  <div className="text-xl font-semibold text-indigo-400">{analyticsData.totals.totalPromptTokens.toLocaleString()}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-zinc-500">Output</div>
                  <div className="text-xl font-semibold text-violet-400">{analyticsData.totals.totalCompletionTokens.toLocaleString()}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Upgrade Plans - Only show if there are available upgrades */}
      {availableUpgrades.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Crown className="h-4.5 w-4.5 text-amber-400" />
            <h4 className="font-semibold text-white">Upgrade Options</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {availableUpgrades.map((planKey) => {
              const planDetails = PLAN_DETAILS[planKey];
              return (
                <div 
                  key={planKey} 
                  className="bg-black/40 border border-zinc-800/50 hover:border-zinc-700/60 rounded-xl p-4 transition-all duration-300 hover:scale-[1.01]"
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="text-xl mb-0.5">{planDetails.icon}</div>
                      <h5 className="text-sm font-semibold text-white">{planDetails.name}</h5>
                      <div className="text-lg font-bold text-white mt-1">
                        {planDetails.price}
                        <span className="text-xs text-zinc-500 font-normal">/mo</span>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => void handleUpgrade(planKey as "pro" | "ultra" | "max")}
                      disabled={isLoading}
                      className="bg-zinc-800/80 hover:bg-zinc-700 text-white border border-zinc-700/50 transition-all duration-200"
                    >
                      Upgrade
                    </Button>
                  </div>
                  
                  <Separator className="my-3 bg-zinc-800/50" />
                  
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between">
                      <span className="text-zinc-400">Credits</span>
                      <span className="text-white font-medium">{planDetails.credits.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-zinc-400">Searches</span>
                      <span className="text-white font-medium">{planDetails.searches}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
