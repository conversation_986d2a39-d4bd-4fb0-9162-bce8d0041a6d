import { useState } from "react";
import { useQ<PERSON>y, useMutation, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Edit, Server, CheckCircle2, XCircle, Zap, Activity, Link, Settings } from "lucide-react";
import { Doc } from "../../../convex/_generated/dataModel";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import {
  SettingsCard,
  SettingsCardHeader,
  SettingsCardTitle,
  Settings<PERSON>ardD<PERSON><PERSON>,
  Settings<PERSON>ardContent,
  SettingsCardFooter,
} from "@/components/settings/SettingsCard";
import { ListSettingsSkeleton } from "@/components/settings/SettingsSkeleton";

type N8nServer = Doc<"n8nServers">;

function N8nServerForm({
  server,
  onSave,
  onClose,
}: {
  server?: N8nServer;
  onSave: (data: Partial<N8nServer>) => void;
  onClose: () => void;
}) {
  const [name, setName] = useState(server?.name ?? "");
  const [description, setDescription] = useState(server?.description ?? "");
  const [apiUrl, setApiUrl] = useState(server?.apiUrl ?? "");
  const [apiKey, setApiKey] = useState((server as any)?.apiKey ?? "");
  const [testConnectionResult, setTestConnectionResult] = useState<{
    success: boolean;
    message?: string;
    workflows?: any[];
  } | null>(null);
  const [isTestingN8n, setIsTestingN8n] = useState(false);

  const testN8nConnection = useAction(api.n8nActions.testConnection);

  const handleSubmit = () => {
    onSave({
      name,
      description,
      apiUrl,
      apiKey: apiKey || undefined,
    });
  };

  const handleTestConnection = async () => {
    if (!apiUrl) {
      setTestConnectionResult({
        success: false,
        message: "Please enter an API URL first",
      });
      return;
    }

    setIsTestingN8n(true);
    try {
      const result = await testN8nConnection({
        apiUrl,
        apiKey: apiKey || undefined,
      });
      setTestConnectionResult(result);
    } catch (error) {
      setTestConnectionResult({
        success: false,
        message:
          error instanceof Error ? error.message : "Connection test failed",
      });
    } finally {
      setIsTestingN8n(false);
    }
  };

  return (
    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto bg-gradient-to-br from-zinc-900/95 via-slate-900/90 to-zinc-900/95 backdrop-blur-xl border border-zinc-800/50">
      <DialogHeader className="pb-6">
        <DialogTitle className="text-2xl font-semibold text-white flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center">
            <Server className="h-4 w-4 text-zinc-300" />
          </div>
          {server ? "Edit n8n Server" : "Add n8n Server"}
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-6">
        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-zinc-300">Server Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter server name"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-zinc-300">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Server description (optional)"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors resize-none"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="apiUrl" className="text-sm font-medium text-zinc-300">API URL *</Label>
            <Input
              id="apiUrl"
              value={apiUrl}
              onChange={(e) => setApiUrl(e.target.value)}
              placeholder="https://your-n8n-instance.com"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="apiKey" className="text-sm font-medium text-zinc-300">API Key</Label>
            <Input
              id="apiKey"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Optional API key for authentication"
              className="bg-zinc-900/50 border-zinc-800/60 text-white placeholder-zinc-500 focus:border-zinc-700 transition-colors"
            />
          </div>
          
          {/* Test Connection Section */}
          <div className="space-y-3 pt-4 border-t border-zinc-800/50">
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleTestConnection}
                disabled={isTestingN8n || !apiUrl}
                className="bg-zinc-900/50 border-zinc-800/60 text-zinc-300 hover:bg-zinc-800/50 hover:text-white transition-all duration-200"
              >
                {isTestingN8n ? (
                  <>
                    <Activity className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Test Connection
                  </>
                )}
              </Button>
            </div>
            
            {testConnectionResult && (
              <div className={`relative rounded-xl p-4 border ${
                testConnectionResult.success 
                  ? 'bg-emerald-500/10 border-emerald-500/30 text-emerald-400' 
                  : 'bg-red-500/10 border-red-500/30 text-red-400'
              }`}>
                <div className="flex items-start gap-3">
                  {testConnectionResult.success ? (
                    <CheckCircle2 className="h-5 w-5 mt-0.5" />
                  ) : (
                    <XCircle className="h-5 w-5 mt-0.5" />
                  )}
                  <div className="space-y-2 flex-1">
                    <h4 className="font-medium">
                      {testConnectionResult.success ? "Connection Successful" : "Connection Failed"}
                    </h4>
                    {testConnectionResult.message && (
                      <p className="text-sm opacity-90">{testConnectionResult.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {testConnectionResult?.success &&
              testConnectionResult.workflows &&
              testConnectionResult.workflows.length > 0 && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-zinc-300">
                    Available Workflows ({testConnectionResult.workflows.length})
                  </Label>
                  <div className="max-h-40 overflow-y-auto space-y-2 rounded-xl border border-zinc-800/50 p-3 bg-zinc-900/30">
                    {testConnectionResult.workflows.map((wf) => (
                      <div
                        key={wf.id}
                        className="p-3 rounded-lg bg-zinc-800/30 border border-zinc-700/30 hover:bg-zinc-800/50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="space-y-1 flex-1 min-w-0">
                            <h5 className="text-sm font-medium text-white truncate">{wf.name}</h5>
                            <div className="flex items-center gap-2 text-xs">
                              <code className="text-zinc-400 bg-zinc-800/50 px-1.5 py-0.5 rounded">{wf.id}</code>
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${
                                  wf.active 
                                    ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30" 
                                    : "bg-amber-500/20 text-amber-400 border-amber-500/30"
                                }`}
                              >
                                {wf.active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>

      <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-3 pt-6">
        <Button
          variant="outline"
          onClick={onClose}
          className="bg-zinc-900/50 border-zinc-800/60 text-zinc-300 hover:bg-zinc-800/50 hover:text-white transition-all duration-200"
        >
          Cancel
        </Button>
        <Button
          onClick={() => void handleSubmit()}
          className="bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 transition-all duration-200 hover:scale-105"
        >
          {server ? "Update" : "Save"} Server
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}

export function N8nSection() {
  const rawServers = useQuery(api.n8nServers.list);
  const servers = rawServers ?? [];
  const createServer = useMutation(api.n8nServers.add);
  const updateServer = useMutation(api.n8nServers.update);
  const deleteServer = useMutation(api.n8nServers.remove);
  const toggleServer = useMutation(api.n8nServers.toggle);
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingServer, setEditingServer] = useState<N8nServer | undefined>(
    undefined
  );

  const handleSave = async (data: Partial<N8nServer>) => {
    try {
      if (editingServer) {
        await updateServer({ id: editingServer._id, ...data });
        toast.success("Server updated successfully!");
      } else {
        const serverId = await createServer(data as any);
        toast.success("Server added.");

        /* ------------------------------------------------------------------
         * Ensure server-specific n8n tool group is enabled in preferences
         * ------------------------------------------------------------------ */
        try {
          const prefs = preferences;
          if (prefs && data.name) {
            const safe = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]/g, "_");
            const baseToolId = `n8n_${safe}`;
            const current = prefs.enabledTools ?? [];
            if (!current.includes(baseToolId)) {
              await updatePreferences({
                enabledTools: [...current, baseToolId],
              });
            }
          }
        } catch (err) {
          console.error(
            "Failed to enable n8n server tools in preferences",
            err
          );
        }
      }
      setIsFormOpen(false);
      setEditingServer(undefined);
    } catch (error) {
      toast.error(
        `Failed to ${editingServer ? "update" : "create"} server.`
      );
      console.error(error);
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await deleteServer({ id });
      toast.success("Server deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete server.");
      console.error(error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Info Section */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
        <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/40 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-xl p-6 transition-all duration-300 hover:border-zinc-700/80">
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-4 right-4 w-16 h-16 border border-white/20 rounded-full" />
            <div className="absolute bottom-6 left-6 w-12 h-12 border border-white/10 rounded-full" />
          </div>
          <div className="relative z-10 flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50">
              <Server className="h-6 w-6 text-zinc-300" />
            </div>
            <div className="flex-1 space-y-2">
              <h3 className="text-lg font-semibold text-white">Connect to n8n</h3>
              <p className="text-zinc-400 leading-relaxed">
                Integrate your n8n workflows with ErzenAI to automate tasks. You can use webhooks for simple triggers or the n8n API for more complex integrations.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Card */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
        <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl transition-all duration-300 hover:border-zinc-700/80">
          {/* Enhanced Header */}
          <div className="p-8 pb-6">
            <div className="flex flex-row items-center justify-between space-y-0">
              <div className="space-y-2">
                <h2 className="text-2xl font-semibold text-white tracking-tight">Your n8n Servers</h2>
                <p className="text-zinc-400">Manage your connected n8n servers and workflows.</p>
              </div>
              <Button
                onClick={() => {
                  setEditingServer(undefined);
                  setIsFormOpen(true);
                }}
                className="bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/50 transition-all duration-200 hover:scale-105 shadow-lg"
              >
                <Plus className="mr-2 h-4 w-4" /> Add Server
              </Button>
            </div>
          </div>

          {/* Enhanced Content */}
          <div className="px-8 pb-8">
            {!rawServers ? (
              <ListSettingsSkeleton />
            ) : servers.length === 0 ? (
              <div className="text-center py-16">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-zinc-800/20 to-slate-800/20 rounded-full blur-2xl" />
                  <div className="relative bg-gradient-to-br from-zinc-900/50 to-slate-900/30 rounded-2xl p-12 border border-zinc-800/40">
                    <div className="space-y-6">
                      <div className="w-16 h-16 mx-auto rounded-xl bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50">
                        <Server className="h-8 w-8 text-zinc-400" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold text-white">No n8n servers configured yet</h3>
                        <p className="text-zinc-400 max-w-md mx-auto leading-relaxed">
                          Add your first n8n server to start automating workflows and integrating with your AI assistant.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {servers.map((server, index) => (
                  <div key={server._id} className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/8 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
                    <div className="relative bg-gradient-to-br from-zinc-900/70 via-slate-900/40 to-zinc-900/70 backdrop-blur-sm border border-zinc-800/50 rounded-xl transition-all duration-300 hover:border-zinc-700/70 hover:transform hover:scale-[1.01] hover:shadow-xl">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5">
                        {index % 3 === 0 && (
                          <>
                            <div className="absolute top-4 right-4 w-12 h-12 border border-white/20 rounded-full" />
                            <div className="absolute bottom-6 left-6 w-8 h-8 border border-white/10 rounded-full" />
                          </>
                        )}
                        {index % 3 === 1 && (
                          <>
                            <div className="absolute top-6 left-4 w-6 h-6 bg-white/10 rounded" />
                            <div className="absolute bottom-4 right-6 w-4 h-4 bg-white/8 rounded" />
                          </>
                        )}
                        {index % 3 === 2 && (
                          <>
                            <div className="absolute top-4 left-4 w-16 h-px bg-white/20" />
                            <div className="absolute top-8 left-4 w-12 h-px bg-white/15" />
                          </>
                        )}
                      </div>

                      {/* Header */}
                      <div className="relative z-10 p-6 pb-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4 flex-1 min-w-0">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-zinc-800 to-slate-800 flex items-center justify-center border border-zinc-700/50 flex-shrink-0">
                              <Server className="h-5 w-5 text-blue-400" />
                            </div>
                            <div className="space-y-2 flex-1 min-w-0">
                              <div className="flex items-center gap-3 flex-wrap">
                                <h3 className="text-lg font-semibold text-white truncate">{server.name}</h3>
                                <Badge 
                                  variant={server.isEnabled ? "default" : "secondary"} 
                                  className={`text-xs font-medium transition-colors duration-200 ${
                                    server.isEnabled 
                                      ? "bg-emerald-500/20 text-emerald-400 border-emerald-500/30" 
                                      : "bg-zinc-800/50 text-zinc-400 border-zinc-700/50"
                                  }`}
                                >
                                  {server.isEnabled ? "Enabled" : "Disabled"}
                                </Badge>
                              </div>
                              {server.description && (
                                <p className="text-sm text-zinc-400 leading-relaxed">{server.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingServer(server);
                                setIsFormOpen(true);
                              }}
                              className="text-zinc-400 hover:text-white hover:bg-zinc-800/50 transition-all duration-200"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={server.isEnabled ? "destructive" : "default"}
                              size="sm"
                              onClick={() => void toggleServer({ id: server._id })}
                              className={`transition-all duration-200 ${
                                server.isEnabled 
                                  ? "bg-red-500/20 text-red-400 hover:bg-red-500/30 border-red-500/30" 
                                  : "bg-emerald-500/20 text-emerald-400 hover:bg-emerald-500/30 border-emerald-500/30"
                              }`}
                            >
                              {server.isEnabled ? "Disable" : "Enable"}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                void handleDelete(server._id);
                              }}
                              className="text-zinc-400 hover:text-red-400 hover:bg-red-500/10 transition-all duration-200"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="relative z-10 px-6 pb-6">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 text-sm">
                            <Link className="h-4 w-4 text-zinc-500" />
                            <span className="text-zinc-400 font-medium">API URL:</span>
                            <code className="text-zinc-300 bg-zinc-800/50 px-2 py-1 rounded text-xs font-mono break-all">{server.apiUrl}</code>
                          </div>
                          {(server as any).apiKey && (
                            <div className="flex items-center gap-2 text-sm">
                              <Settings className="h-4 w-4 text-zinc-500" />
                              <span className="text-zinc-400 font-medium">Authentication:</span>
                              <Badge variant="outline" className="text-xs bg-zinc-800/30 text-zinc-400 border-zinc-700/50">
                                API Key Configured
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <N8nServerForm
          server={editingServer}
          onSave={(data) => void handleSave(data)}
          onClose={() => setIsFormOpen(false)}
        />
      </Dialog>
    </div>
  );
}