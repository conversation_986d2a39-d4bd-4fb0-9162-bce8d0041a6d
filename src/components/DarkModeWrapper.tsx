import React, { useEffect, useRef } from 'react';

interface DarkModeWrapperProps {
  children: React.ReactNode;
  className?: string;
  /**
   * Whether to force all child components to use dark mode styling.
   * Default: true
   */
  forceDarkMode?: boolean;
}

/**
 * Wrapper component that forces dark mode for components that are designed
 * exclusively for dark themes (like Homepage and SettingsPage).
 * 
 * This ensures these components always render properly regardless of the 
 * global theme setting by:
 * 1. Adding the 'dark' class to create a dark mode context
 * 2. Ensuring proper CSS cascade for dark mode variables
 * 
 * Usage:
 * ```tsx
 * <DarkModeWrapper>
 *   <SomeDarkOnlyComponent />
 * </DarkModeWrapper>
 * ```
 */
export function DarkModeWrapper({ 
  children, 
  className = "", 
  forceDarkMode = true 
}: DarkModeWrapperProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (forceDarkMode && wrapperRef.current) {
      // Ensure the dark class is always present
      wrapperRef.current.classList.add('dark');
      
      // Force dark mode CSS variables to be applied
      const computedStyle = getComputedStyle(wrapperRef.current);
      const bgColor = computedStyle.getPropertyValue('--background');
      
      // If background variable isn't properly set, force it
      if (!bgColor || bgColor.trim() === '') {
        wrapperRef.current.style.setProperty('--background', '224 71% 4%');
        wrapperRef.current.style.setProperty('--foreground', '213 31% 91%');
      }
    }
  }, [forceDarkMode]);

  const finalClassName = forceDarkMode 
    ? `dark ${className}`.trim()
    : className;

  return (
    <div ref={wrapperRef} className={finalClassName}>
      {children}
    </div>
  );
} 