import { usePaginatedQuery, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import {
  Hash,
  MessageSquare,
  Pin,
} from "lucide-react";
import React, { useCallback } from "react";
import { formatDistanceToNow } from "date-fns";

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
  CommandItem,
  CommandSeparator,
} from "@/components/ui/command";

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void;
}

export function CommandPalette({
  open,
  onOpenChange,
  onSelectConversation,
}: CommandPaletteProps) {
  // ------------------------------
  // Local state & helpers (cmdk)
  // ------------------------------
  const [query, setQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");

  // Debounce the user input for smoother UX
  React.useEffect(() => {
    const id = setTimeout(() => setDebouncedQuery(query.trim()), 200);
    return () => clearTimeout(id);
  }, [query]);

  // Fetch data
  const { results: conversations } = usePaginatedQuery(
    api.conversations.listWithMessageCounts,
    open && debouncedQuery === "" ? { paginationOpts: { numItems: 10, cursor: null } } : "skip",
    { initialNumItems: 10 }
  );

  const searchResults = useQuery(
    api.conversations.searchConversations,
    open && debouncedQuery !== "" ? { query: debouncedQuery, limit: 15 } : "skip"
  );

  const isLoading = debouncedQuery !== "" && searchResults === undefined;
  const page = debouncedQuery !== "" ? searchResults ?? [] : conversations ?? [];

  const pinned = page.filter((c: any) => c.isPinned);
  const others = page.filter((c: any) => !c.isPinned);

  const handleOpenChange = (o: boolean) => {
    onOpenChange(o);
    if (!o) {
      setQuery("");
      setDebouncedQuery("");
    }
  };

  // Helpers for highlighting query inside titles
  const escapeRegExp = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const highlight = (text: string) => {
    if (!debouncedQuery) return text;
    const regex = new RegExp(`(${escapeRegExp(debouncedQuery)})`, "gi");
    const parts = text.split(regex);
    return parts.map((part, idx) =>
      regex.test(part) ? (
        <span key={idx} className="text-primary font-semibold">
          {part}
        </span>
      ) : (
        <React.Fragment key={idx}>{part}</React.Fragment>
      )
    );
  };

  const renderItem = (conv: any) => (
    <CommandItem
      key={conv._id}
      value={conv._id}
      onSelect={() => {
        onSelectConversation(conv._id);
        handleOpenChange(false);
      }}
      className="relative flex items-center gap-2 rounded-sm px-2 py-2 transition-colors duration-150 ease-in-out hover:bg-accent/50 data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground"
    >
      {conv.isPinned ? (
        <Pin className="size-4 shrink-0 text-muted-foreground" />
      ) : (
        <MessageSquare className="size-4 shrink-0 text-muted-foreground" />
      )}
      <span className="truncate flex-1 text-sm font-medium">
        {highlight(conv.title)}
      </span>
      <div className="flex flex-col items-end text-[10px] leading-tight text-muted-foreground">
        {conv.messageCount > 0 && (
          <span className="flex items-center gap-1 font-mono">
            <Hash size={10} /> {conv.messageCount}
          </span>
        )}
        <span>{formatDistanceToNow(new Date(conv.lastMessageAt), { addSuffix: true })}</span>
      </div>
    </CommandItem>
  );

  return (
    <CommandDialog
      open={open}
      onOpenChange={handleOpenChange}
      className="sm:max-w-md"
    >
      <CommandInput
        placeholder="Search conversations…"
        value={query}
        onValueChange={setQuery}
      />
      <CommandList className="max-h-[50vh]">
        {isLoading && (
          <div className="p-4 text-center text-sm text-muted-foreground">Searching…</div>
        )}

        {!isLoading && page.length === 0 && (
          <CommandEmpty>No conversations found.</CommandEmpty>
        )}

        {!isLoading && page.length > 0 && (
          <>
            {pinned.length > 0 && (
              <CommandGroup heading="Pinned">
                {pinned.map(renderItem)}
              </CommandGroup>
            )}
            {pinned.length > 0 && others.length > 0 && <CommandSeparator />}
            {others.length > 0 && (
              <CommandGroup
                heading={debouncedQuery ? "Search Results" : "Recent"}
              >
                {others.map(renderItem)}
              </CommandGroup>
            )}
          </>
        )}
      </CommandList>
    </CommandDialog>
  );
} 