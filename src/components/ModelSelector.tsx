import React, { useState, useRef, useEffect, useMemo } from "react";
import { createPortal } from "react-dom";
import { ChevronDown, Star, Search, Eye, Wrench, Brain } from "lucide-react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { TooltipProvider, Toolt<PERSON>, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  PROVIDER_CONFIGS,
  getModelInfo,
  getModelsByGroup,
  getBestAvailableProvider,
  ModelInfo,
} from "@/lib/models";
import { ModelTooltip } from "./ModelTooltip";
import { getCompanyIcon, getProviderIcon } from "@/lib/model-ui";

// A simple hook to check for desktop screen sizes
const useIsDesktop = () => {
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 768)
  useEffect(() => {
    const handleResize = () => setIsDesktop(window.innerWidth >= 768)
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])
  return isDesktop
}

type AiProvider =
  | "openai"
  | "anthropic"
  | "google"
  | "openrouter"
  | "groq"
  | "deepseek"
  | "grok"
  | "cohere"
  | "mistral"
  | "cerebras"
  | "github"

interface ModelSelectorProps {
  selectedProvider: string;
  selectedModel: string;
  onModelSelect: (provider: string, model: string) => void;
  disabled?: boolean;
}

export function ModelSelector({ selectedProvider, selectedModel, onModelSelect, disabled }: ModelSelectorProps) {
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<"favorites" | "all">("favorites");
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState<
    { bottom: number; left: number; width: number } | null
  >(null);
  
  const modelSelectorRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isDesktop = useIsDesktop();

  const preferences = useQuery(api.preferences.get);
  const userApiKeys = useQuery(api.apiKeys.list) || [];
  const customProviders = useQuery(api.customProviders.list) || [];
  const toggleFavoriteModel = useMutation(api.preferences.toggleFavoriteModel);
  const getAvailableProviders = useAction(api.ai.getAvailableProviders);

  const favoriteModels = preferences?.favoriteModels || [];

  // Helper to check if a model should be shown in the UI
  const isModelVisible = (modelId: string) => !getModelInfo(modelId).hidden;

  // Get current model info
  const currentModelInfo = getModelInfo(selectedModel);

  // Add memoized modelInfo for auto
  const autoModelInfo = useMemo(() => getModelInfo("auto"), []);


  // Load available providers (cached to avoid refetching on every render)
  const [providersLoaded, setProvidersLoaded] = useState(false)
  useEffect(() => {
    if (providersLoaded) return // Only load once
    const loadProviders = async () => {
      try {
        const providers = await getAvailableProviders()
        setAvailableProviders(providers)
        setProvidersLoaded(true)
      } catch (error) {
        console.error("Failed to load available providers:", error)
        const fallbackProviders = Object.keys(PROVIDER_CONFIGS).filter((provider) => {
          if (provider === "openai" || provider === "google") return true
          return userApiKeys.some((key: { provider: string; hasKey: boolean }) => key.provider === provider && key.hasKey)
        })
        setAvailableProviders(fallbackProviders)
        setProvidersLoaded(true)
      }
    }
    void loadProviders()
  }, [getAvailableProviders, userApiKeys, providersLoaded])

  // Refresh providers when userApiKeys or customProviders change
  useEffect(() => {
    if (providersLoaded) {
      setProvidersLoaded(false) // Force reload when dependencies change
    }
  }, [userApiKeys.length, customProviders.length])

  // Model selector outside click handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node
      const isClickInsideButton = modelSelectorRef.current?.contains(target)
      const isClickInsideDropdown = dropdownRef.current?.contains(target)
      if (!isClickInsideButton && !isClickInsideDropdown) {
        setShowModelSelector(false)
        setSearchQuery("")
        setDropdownPosition(null)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleToggleModelSelector = () => {
    if (disabled) return // Disable model selector when disabled

    if (showModelSelector) {
      setShowModelSelector(false)
    } else {
      if (isDesktop && modelSelectorRef.current) {
        const rect = modelSelectorRef.current.getBoundingClientRect()
        
        // Keep a small 8px margin on every side
        const SIDE_MARGIN = 8
        const MAX_WIDTH = 400 // px – desired max width
        const dropdownWidth = Math.min(MAX_WIDTH, window.innerWidth - SIDE_MARGIN * 2)

        // Clamp the dropdown so it never overflows the right edge
        const clampedLeft = Math.min(
          Math.max(SIDE_MARGIN, rect.left),
          window.innerWidth - dropdownWidth - SIDE_MARGIN,
        )

        setDropdownPosition({
          bottom: window.innerHeight - rect.top + SIDE_MARGIN,
          left: clampedLeft,
          width: dropdownWidth,
        })
      }
      setShowModelSelector(true)
    }
  }

  const handleModelSelectInternal = (provider: AiProvider, model: string) => {
    if (disabled) return // Prevent model changes when disabled
    
    onModelSelect(provider, model)
    setShowModelSelector(false)
    setSearchQuery("")
  }

  const handleToggleFavorite = async (e: React.MouseEvent, provider: string, model: string) => {
    e.stopPropagation()
    if (disabled) return // Prevent changes when disabled
    await toggleFavoriteModel({ provider, model })
  }

  const isFavorite = (provider: string, model: string) => {
    return favoriteModels.some((fav) => fav.provider === provider && fav.model === model)
  }

  // Build a merged model group map that also includes models from custom providers.
  const modelsByGroup = useMemo(() => {
    // Start with built-in grouping
    const baseGroups = getModelsByGroup();

    // Merge in custom provider models (only active ones)
    customProviders
      .filter((cp: any) => cp?.isActive)
      .forEach((cp: any) => {
        const groupName: string = cp.displayName || cp.name;

        if (!Array.isArray(cp.models)) return;

        // Ensure the group exists
        if (!baseGroups[groupName]) {
          baseGroups[groupName] = [];
        }

        cp.models.forEach((modelId: string) => {
          // Skip duplicates
          if (baseGroups[groupName].some((m) => m.id === modelId)) return;

          // Generate base info (fallback if unknown)
          const baseInfo: ModelInfo = getModelInfo(modelId);

          // Attach custom provider metadata so availability filtering works
          const modelInfo: ModelInfo = {
            ...baseInfo,
            provider: cp.name,
            groupName,
            modelProviders: [
              {
                provider: cp.name,
                modelId,
                priority: 1,
              },
            ],
          };

          baseGroups[groupName].push(modelInfo);
        });
      });

    return baseGroups;
  }, [customProviders]);

  const getFavoriteModels = () => {
    const filtered = favoriteModels.filter(
      (fav) =>
        availableProviders.includes(fav.provider) &&
        isModelVisible(fav.model) &&
        PROVIDER_CONFIGS[fav.provider as AiProvider]?.models.includes(fav.model),
    )
    if (!searchQuery) return filtered
    return filtered.filter((fav) => {
      const modelInfo = getModelInfo(fav.model)
      return (
        modelInfo.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        modelInfo.groupName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        fav.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
        // Search by available providers
        (modelInfo.modelProviders || []).some(provider => 
          provider.provider.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    })
  }

  const getFilteredCompanies = () => {
    const companies = Object.keys(modelsByGroup).filter(groupName => {
      // Check if any models in this group are available through current providers
      return modelsByGroup[groupName].some(model => 
        (model.modelProviders || []).some(provider => availableProviders.includes(provider.provider))
      )
    })

    if (!searchQuery) return companies

    return companies.filter((groupName) => {
      // Search by company name
      if (groupName.toLowerCase().includes(searchQuery.toLowerCase())) {
        return true
      }
      
      // Search by models in this group
      return modelsByGroup[groupName].some((model) => {
        return (
          model.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          model.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          // Search by available providers
          (model.modelProviders || []).some(provider => 
            provider.provider.toLowerCase().includes(searchQuery.toLowerCase())
          )
        )
      })
    })
  }

  const getFilteredModels = (groupName: string) => {
    const models = modelsByGroup[groupName] || []
    
    // Filter models that are available through current providers
    const availableModels = models.filter(model => 
      (model.modelProviders || []).some(provider => availableProviders.includes(provider.provider))
    )
    
    if (!searchQuery) return availableModels
    
    return availableModels.filter((model) => {
      return (
        model.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        // Search by available providers
        (model.modelProviders || []).some(provider => 
          provider.provider.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    })
  }

  const IconComponent = getProviderIcon(selectedProvider, customProviders)

  return (
    <div className="relative" ref={modelSelectorRef}>
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleToggleModelSelector}
              disabled={disabled}
              className={cn(
                "h-8 sm:h-9 px-2.5 sm:px-3 py-0 rounded-full hover:bg-muted/30 text-xs sm:text-sm font-medium",
                disabled && "opacity-50 cursor-not-allowed",
              )}
            >
              {/* Closed state content (no leading icon) */}
              <div className="flex items-center gap-1.5 max-w-[180px] sm:max-w-[220px]">
                <span className="truncate text-foreground/90">
                  {currentModelInfo.displayName}
                </span>
                {currentModelInfo.parameters && (
                  <span className="hidden sm:inline text-xs text-muted-foreground/80 bg-muted/50 px-1.5 py-0.5 rounded truncate">
                    {currentModelInfo.parameters}
                  </span>
                )}
                {!disabled && (
                  <ChevronDown
                    size={12}
                    className={cn("ml-0.5 text-muted-foreground/70 transition-transform", showModelSelector && "rotate-180")}
                  />
                )}
                {disabled && (
                  <Badge variant="secondary" className="ml-1 text-[10px] px-1.5 py-0.5">
                    Locked
                  </Badge>
                )}
              </div>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">{currentModelInfo.displayName}</TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {showModelSelector &&
        !isDesktop &&
        createPortal(
          <div className="fixed inset-0 bg-black/60 z-[9998]" onClick={() => setShowModelSelector(false)} />,
          document.body,
        )}

      {showModelSelector &&
        !disabled &&
        createPortal(
          <div
            ref={dropdownRef}
            style={
              isDesktop && dropdownPosition
                ? {
                    position: "fixed",
                    bottom: `${dropdownPosition.bottom}px`,
                    left: `${dropdownPosition.left}px`,
                    width: `${dropdownPosition.width}px`,
                  }
                : {}
            }
            className={cn(
              "bg-popover border rounded-xl shadow-xl z-[9999] overflow-hidden flex flex-col",
              isDesktop
                ? "h-[50vh] w-full max-w-[400px]"
                : "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-md max-h-[80vh]",
            )}
          >
            {/* Search */}
            <div className="p-3 border-b border-border/50">
              <div className="relative">
                <Search
                  size={14}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                />
                <input
                  type="text"
                  placeholder="Search models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-border/50 text-sm">
              <button
                onClick={() => setActiveTab("favorites")}
                className={cn(
                  "flex-1 px-4 py-3 font-medium transition-colors",
                  activeTab === "favorites"
                    ? "bg-primary/10 text-primary border-b-2 border-primary"
                    : "text-muted-foreground hover:text-foreground",
                )}
              >
                <Star size={14} className="inline mr-2" />
                Favorites
              </button>
              <button
                onClick={() => setActiveTab("all")}
                className={cn(
                  "flex-1 px-4 py-3 font-medium transition-colors",
                  activeTab === "all"
                    ? "bg-primary/10 text-primary border-b-2 border-primary"
                    : "text-muted-foreground hover:text-foreground",
                )}
              >
                All Models
              </button>
            </div>

            <div className="overflow-y-auto">
              {activeTab === "favorites" && !searchQuery ? (
                <div className="p-2">
                  <ModelTooltip modelInfo={autoModelInfo}>
                    <button
                      onClick={() => handleModelSelectInternal("system" as AiProvider, "auto")}
                      className={cn(
                        "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors mb-1",
                        selectedModel === "auto" ? "bg-primary/10 text-primary" : "text-foreground",
                      )}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        {(() => {
                          const IconComp = getProviderIcon("system", customProviders);
                          return <IconComp size={14} />;
                        })()}
                        <span className="font-medium truncate">Auto</span>
                      </div>
                    </button>
                  </ModelTooltip>
                  {getFavoriteModels().length > 0 ? (
                    getFavoriteModels().map((fav) => {
                      const modelInfo = getModelInfo(fav.model)
                      return (
                        <ModelTooltip key={`${fav.provider}:${fav.model}`} modelInfo={modelInfo}>
                          <button
                            onClick={() =>
                              handleModelSelectInternal(fav.provider as AiProvider, fav.model)
                            }
                            className={cn(
                              "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors",
                              selectedProvider === fav.provider && selectedModel === fav.model
                                ? "bg-primary/10 text-primary"
                                : "text-foreground",
                            )}
                          >
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                              {(() => {
                                const IconComp = getProviderIcon(fav.provider, customProviders)
                                return <IconComp size={14} />
                              })()}
                              <span className="font-medium truncate">{modelInfo.displayName}</span>
                              <div className="flex items-center gap-1 flex-shrink-0">
                                {modelInfo.parameters && (
                                  <span className="text-xs text-muted-foreground bg-muted/50 px-1.5 py-0.5 rounded">
                                    {modelInfo.parameters}
                                  </span>
                                )}
                                {modelInfo.supportsTools && (
                                  <Wrench size={12} className="text-green-500" />
                                )}
                                {modelInfo.isMultimodal && <Eye size={12} className="text-blue-500" />}
                                {modelInfo.supportsThinking && (
                                  <Brain size={12} className="text-purple-500" />
                                )}
                              </div>
                            </div>
                            <Star size={14} className="text-yellow-500 fill-current ml-2" />
                          </button>
                        </ModelTooltip>
                      )
                    })
                  ) : (
                    <div className="p-4 text-sm text-muted-foreground text-center">
                      {"No favorites yet. Star models below to add them here."}
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-2">
                  <ModelTooltip modelInfo={autoModelInfo}>
                    <button
                      onClick={() => handleModelSelectInternal("system" as AiProvider, "auto")}
                      className={cn(
                        "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors mb-3",
                        selectedModel === "auto" ? "bg-primary/10 text-primary" : "text-foreground",
                      )}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        {(() => {
                          const IconComp = getProviderIcon("system", customProviders);
                          return <IconComp size={14} />;
                        })()}
                        <span className="font-medium truncate">Auto</span>
                      </div>
                    </button>
                  </ModelTooltip>
                  {getFilteredCompanies().length > 0 ? (
                    getFilteredCompanies().map((groupName) => {
                      const filteredModels = getFilteredModels(groupName)
                      if (filteredModels.length === 0) return null
                      return (
                        <div key={groupName} className="mb-3">
                          <div className="px-3 py-2 bg-muted/30 text-sm font-medium text-muted-foreground flex items-center gap-3 rounded-lg">
                            {(() => {
                              const IconComp = getCompanyIcon(groupName)
                              return <IconComp size={14} />
                            })()}
                            {groupName}
                          </div>
                          <div className="mt-1 space-y-1">
                            {filteredModels.map((model) => {
                              // Get the best available provider for this model
                              const bestProvider = getBestAvailableProvider(model.id, availableProviders)
                              const primaryProvider = bestProvider?.provider || model.provider
                              
                              return (
                                <ModelTooltip key={model.id} modelInfo={model}>
                                  <button
                                    onClick={() =>
                                      handleModelSelectInternal(primaryProvider as AiProvider, model.id)
                                    }
                                    className={cn(
                                      "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors",
                                      selectedModel === model.id
                                        ? "bg-primary/10 text-primary"
                                        : "text-foreground",
                                    )}
                                  >
                                    <div className="flex items-center gap-2 flex-1 min-w-0">
                                      <span className="truncate">{model.displayName}</span>
                                      <div className="flex items-center gap-1 flex-shrink-0">
                                        {model.parameters && (
                                          <span className="text-xs text-muted-foreground bg-muted/50 px-1.5 py-0.5 rounded">
                                            {model.parameters}
                                          </span>
                                        )}
                                        {model.supportsTools && (
                                          <Wrench size={12} className="text-green-500" />
                                        )}
                                        {model.isMultimodal && (
                                          <Eye size={12} className="text-blue-500" />
                                        )}
                                        {model.supportsThinking && (
                                          <Brain size={12} className="text-purple-500" />
                                        )}
                                      </div>
                                    </div>
                                    <div
                                      onClick={(e) => void handleToggleFavorite(e, primaryProvider, model.id)}
                                      className="p-1 hover:bg-muted rounded-md flex-shrink-0 cursor-pointer"
                                    >
                                      <Star
                                        size={14}
                                        className={
                                          isFavorite(primaryProvider, model.id)
                                            ? "text-yellow-500 fill-current"
                                            : "text-muted-foreground"
                                        }
                                      />
                                    </div>
                                  </button>
                                </ModelTooltip>
                              )
                            })}
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    <div className="p-4 text-sm text-muted-foreground text-center">
                      {searchQuery ? `No models found matching "${searchQuery}".` : "No models available."}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>,
          document.body,
        )}
    </div>
  );
}