import { useState, memo, useMemo, useEffect, useRef } from "react";
import { useMutation, useAction, useQuery, usePaginatedQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import {
  Plus,
  MessageSquare,
  Trash2,
  Edit2,
  Copy,
  MoreHorizontal,
  X,
  Hash,
  Clock,
  Search,
  Sparkles,
  User,
  Eye,
  EyeOff,
  ChevronDown,
  Pin,
  PinOff,
  Share2,
  Download,
  ExternalLink,
  Library,
  Rocket,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from "@/components/ui/dropdown-menu";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter 
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

function useDebounce<T>(value: T, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
}

interface SidebarProps {
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void | Promise<void>;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
}

export const Sidebar = memo(function Sidebar({
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  isSidebarOpen,
  onToggleSidebar,
}: SidebarProps) {
  const [editingId, setEditingId] = useState<Id<"conversations"> | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState<Id<"conversations"> | null>(null);
  const [searchInput, setSearchInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [highlightIndex, setHighlightIndex] = useState<number>(() => -1);
  
  // New state for share and export
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [activeConversationId, setActiveConversationId] = useState<Id<"conversations"> | null>(null);
  const [shareUrl, setShareUrl] = useState("");

  const navigateTo = (path: string) => {
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  // Get user data and preferences
  const user = useQuery(api.auth.loggedInUser);
  const preferences = useQuery(api.preferences.get);
  const usage = useQuery(api.usage.get);
  const updatePreferences = useMutation(api.preferences.update);

  // Memoize empty args object to avoid unnecessary re-renders
  const convListArgs = useMemo(() => ({}), []);

  const debouncedOpen = useDebounce(isSidebarOpen, 300);

  const {
    results: conversations,
    status,
    loadMore,
  } = usePaginatedQuery(
    api.conversations.listWithMessageCounts,
    debouncedOpen ? convListArgs : "skip",
    { initialNumItems: 15 }
  );

  const deleteConversation = useMutation(api.conversations.remove);
  const updateTitle = useMutation(api.conversations.updateTitle);
  const duplicateConversation = useMutation(api.conversations.duplicate);
  const generateTitle = useAction(api.conversations.generateTitle);
  
  // New mutations for pin, share, export
  const togglePin = useMutation(api.conversations.togglePin);
  const shareConversation = useMutation(api.conversations.shareConversation);
  const unshareConversation = useMutation(api.conversations.unshareConversation);
  const markExported = useMutation(api.conversations.markExported);
  
  // Query for export data when dialog is open
  const exportData = useQuery(
    api.conversations.exportConversation, 
    activeConversationId && exportDialogOpen ? { conversationId: activeConversationId } : "skip"
  );

  // after existing paginated query block, insert searchResults query
  const searchResults = useQuery(
    api.conversations.searchConversations,
    searchQuery ? { query: searchQuery, limit: 15 } : "skip"
  );

  // Replace filteredConversations logic
  const sourceConversations = searchQuery ? (searchResults ?? []) : conversations;

  const filteredConversations = sourceConversations; // already limited by backend

  // Sort conversations (pinned first, then newest) – memoized to avoid unnecessary work on every render
  const sortedFilteredConversations = useMemo(() => {
    // Defensive copy in case the source array comes from Convex cache (don't mutate it)
    const convs = filteredConversations ? [...filteredConversations] : [];

    return convs.sort((a: any, b: any) => {
      // Pinned first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Newest last message first
      return b.lastMessageAt - a.lastMessageAt;
    });
  }, [filteredConversations]);

  const conversationIds = useMemo(() => 
    sortedFilteredConversations.map(c => c._id).join(','),
    [sortedFilteredConversations]
  );

  useEffect(() => {
    setHighlightIndex(sortedFilteredConversations.length > 0 ? 0 : -1);
  }, [searchQuery, conversationIds]);

  // Sync highlight with currentConversationId if it's within the already loaded list
  useEffect(() => {
    if (!currentConversationId) {
      setHighlightIndex(-1);
      return;
    }
    const idx = sortedFilteredConversations.findIndex(
      (c: any) => c._id === currentConversationId,
    );
    setHighlightIndex(idx);
  }, [currentConversationId, sortedFilteredConversations]);

  const handleDelete = async () => {
    if (conversationToDelete) {
      await deleteConversation({ conversationId: conversationToDelete });
      if (currentConversationId === conversationToDelete) {
        const firstConversation = sortedFilteredConversations.filter((c: any) => c._id !== conversationToDelete)[0];
        if (firstConversation) {
          onSelectConversation(firstConversation._id);
        }
      }
      setConversationToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  const toggleUserInfoVisibility = async () => {
    if (preferences) {
      await updatePreferences({
        aiProvider: preferences.aiProvider,
        model: preferences.model,
        temperature: preferences.temperature,
        enabledTools: preferences.enabledTools,
        favoriteModels: preferences.favoriteModels,
        hideUserInfo: !preferences.hideUserInfo,
        showToolOutputs: preferences.showToolOutputs,
        showMessageMetadata: preferences.showMessageMetadata,
        showThinking: preferences.showThinking,
        systemPrompt: preferences.systemPrompt,
        useCustomSystemPrompt: preferences.useCustomSystemPrompt,
        theme: preferences.theme,
        colorTheme: preferences.colorTheme,
      });
    }
  };
  
  const openDeleteDialog = (id: Id<"conversations">) => {
    setConversationToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDuplicate = async (id: Id<"conversations">) => {
    const newConversationId = await duplicateConversation({ conversationId: id });
    onSelectConversation(newConversationId);
  };

  const handleEdit = (conversation: any) => {
    setEditingId(conversation._id);
    setEditTitle(conversation.title);
  };

  const handleSaveEdit = async () => {
    if (editingId && editTitle.trim()) {
      await updateTitle({
        conversationId: editingId,
        title: editTitle.trim(),
      });
      setEditingId(null);
      setEditTitle("");
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditTitle("");
  };

  const handleGenerateTitle = async (conversationId: Id<"conversations">) => {
    try {
      await generateTitle({
        conversationId,
      });
    } catch (error) {
      console.error("Failed to generate title:", error);
    }
  };

  // New handler functions for pin, share, export/import
  const handleTogglePin = async (conversationId: Id<"conversations">) => {
    try {
      await togglePin({ conversationId });
    } catch (error) {
      console.error("Failed to toggle pin:", error);
    }
  };

  const handleShare = async (conversationId: Id<"conversations">) => {
    try {
      const shareId = await shareConversation({ conversationId });
      const url = `${window.location.origin}/shared/${shareId}`;
      setShareUrl(url);
      setActiveConversationId(conversationId);
      setShareDialogOpen(true);
    } catch (error) {
      console.error("Failed to share conversation:", error);
    }
  };

  const handleUnshare = async (conversationId: Id<"conversations">) => {
    try {
      await unshareConversation({ conversationId });
      setShareDialogOpen(false);
    } catch (error) {
      console.error("Failed to unshare conversation:", error);
    }
  };

  const handleExport = async (conversationId: Id<"conversations">) => {
    setActiveConversationId(conversationId);
    setExportDialogOpen(true);
  };

  const handleDownloadExport = async () => {
    if (exportData && activeConversationId) {
      try {
        await markExported({ conversationId: activeConversationId });
        
        const conversation = conversations.find(c => c._id === activeConversationId);
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json',
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation-${conversation?.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'export'}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        setExportDialogOpen(false);
      } catch (error) {
        console.error("Failed to export conversation:", error);
      }
    }
  };



  const formatLastMessageTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  // Function to truncate title with smart word breaking
  const truncateTitle = (title: string, maxLength: number = 33) => {
    if (title.length <= maxLength) return title;
    
    // Try to break at word boundary
    const truncated = title.substring(0, maxLength);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    
    if (lastSpaceIndex > maxLength * 0.6) {
      return truncated.substring(0, lastSpaceIndex) + '...';
    }
    
    return truncated + '...';
  };

  // New: group conversations by date label
  const groupLabelFor = (timestamp: number) => {
    const d = new Date(timestamp);
    const now = new Date();
    const startOfDay = (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
    const dayMs = 24 * 60 * 60 * 1000;

    const todayStart = startOfDay(now);
    const yesterdayStart = todayStart - dayMs;
    const weekStart = todayStart - 6 * dayMs;

    const ts = d.getTime();
    if (ts >= todayStart) return 'Today';
    if (ts >= yesterdayStart) return 'Yesterday';
    if (ts >= weekStart) return 'This week';
    return 'Earlier';
  };

  const groupedConversations = useMemo(() => {
    const groups: Record<string, any[]> = {};
    (sortedFilteredConversations || []).forEach((c: any) => {
      const label = groupLabelFor(c.lastMessageAt);
      if (!groups[label]) groups[label] = [];
      groups[label].push(c);
    });
    const order = ['Today', 'Yesterday', 'This week', 'Earlier'];
    return order.filter((k) => groups[k]?.length).map((k) => ({ label: k, items: groups[k] }));
  }, [sortedFilteredConversations]);

  // Helper to get global index from conversation ID
  const getGlobalIndex = (conversationId: string) => {
    return sortedFilteredConversations.findIndex((c: any) => c._id === conversationId);
  };

  return (
    <TooltipProvider>
      <div
        className={cn(
          // Container: refined glassmorphism with subtle gradients
          "h-full flex flex-col w-80 relative overflow-hidden border-r border-border/30",
          "bg-[radial-gradient(1200px_400px_at_-20%_-10%,hsl(var(--primary)/0.04),transparent_70%)]",
          "backdrop-blur-xl bg-background/95",
          "before:absolute before:inset-0 before:pointer-events-none before:opacity-40",
          "before:bg-[radial-gradient(800px_220px_at_110%_10%,hsl(var(--accent)/0.04),transparent_70%)]"
        )}
      >
        {/* Header with close button only on mobile */}
        <div className="px-3 py-2 flex justify-end lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleSidebar}
            className="h-8 w-8 hover:bg-muted/50"
          >
            <X size={16} />
          </Button>
        </div>

        {/* New Chat + Library */}
        <div className="p-3 flex items-center gap-2">
          <Button
            onClick={() => void onNewConversation()}
            className={cn(
              "flex-1 justify-start gap-2 h-10 rounded-xl font-semibold",
              "bg-gradient-to-br from-primary/90 via-primary/80 to-primary/70",
              "hover:from-primary hover:via-primary/90 hover:to-primary/80",
              "text-primary-foreground shadow-sm transition-all duration-150"
            )}
          >
            <Plus className="h-4 w-4" />
            <span className="text-sm">New chat</span>
          </Button>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => navigateTo("/library")}
                variant="outline"
                size="icon"
                className="h-10 w-10 rounded-xl bg-background/60 hover:bg-muted/60 transition-colors duration-150"
              >
                <Library className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Library</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Search */}
        <div className="px-3 pb-3">
          <div className="relative group">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground/60 h-4 w-4" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchInput}
              onChange={(e) => {
                const val = e.target.value;
                setSearchInput(val);
                if (val === "") {
                  setSearchQuery("");
                  setHighlightIndex(-1);
                }
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  const trimmed = searchInput.trim();
                  const debounceSet = setTimeout(() => {
                    if (trimmed !== searchQuery) {
                      setSearchQuery(trimmed);
                      setHighlightIndex(0);
                    } else if (highlightIndex >= 0 && highlightIndex < sortedFilteredConversations.length) {
                      onSelectConversation(sortedFilteredConversations[highlightIndex]._id);
                    }
                  }, 300);
                  return () => clearTimeout(debounceSet);
                } else if (e.key === "Escape") {
                  setSearchInput("");
                  setSearchQuery("");
                  setHighlightIndex(-1);
                } else if (e.key === "ArrowDown") {
                  e.preventDefault();
                  if (sortedFilteredConversations.length > 0) {
                    setHighlightIndex((prev) => (prev + 1) % sortedFilteredConversations.length);
                  }
                } else if (e.key === "ArrowUp") {
                  e.preventDefault();
                  if (sortedFilteredConversations.length > 0) {
                    setHighlightIndex((prev) => (prev <= 0 ? sortedFilteredConversations.length - 1 : prev - 1));
                  }
                }
              }}
              className={cn(
                "w-full pl-9 pr-3 py-2.5 text-sm rounded-xl",
                "bg-background/60 border border-border/40",
                "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40",
                "placeholder:text-muted-foreground/60 transition-all duration-150"
              )}
            />
          </div>
        </div>

        {/* Conversations (Grouped & simplified) */}
        <ScrollArea className="flex-1 px-3">
          <div className="pb-2 space-y-3">
            {groupedConversations.map(({ label, items }) => (
              <div key={label} className="space-y-1">
                <div className="px-2 flex items-center justify-between">
                  <div className="text-label-sm text-muted-foreground/70">{label}</div>
                  <div className="text-[10px] text-muted-foreground/60 bg-muted/30 px-1.5 py-0.5 rounded-full">{items.length}</div>
                </div>
                {items.map((conversation: any) => {
                  const globalIndex = getGlobalIndex(conversation._id);
                  return (
                    <div
                      key={conversation._id}
                      className={cn(
                        "group relative rounded-lg overflow-hidden transition-all duration-150",
                        (currentConversationId === conversation._id || globalIndex === highlightIndex)
                          ? "bg-primary/12"
                          : "hover:bg-muted/40"
                      )}
                    >
                      {editingId === conversation._id ? (
                        <div className="p-2">
                          <input
                            type="text"
                            value={editTitle}
                            onChange={(e) => setEditTitle(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") void handleSaveEdit();
                              if (e.key === "Escape") handleCancelEdit();
                            }}
                            onBlur={() => void handleSaveEdit()}
                            className="w-full px-2.5 py-2 text-sm border border-primary/40 rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/30 font-medium"
                            autoFocus
                          />
                        </div>
                      ) : (
                        <Button
                          variant="ghost"
                          onClick={() => {
                            onSelectConversation(conversation._id);
                            setHighlightIndex(globalIndex);
                          }}
                          className="w-full justify-between h-auto py-2 px-3 hover:bg-transparent"
                        >
                          <div className="flex items-center gap-1.5 min-w-0 flex-1">
                            {conversation.isGenerating && (
                              <Loader2 size={11} className="text-primary animate-spin shrink-0" />
                            )}
                            {conversation.isPinned && (
                              <Pin size={10} className="text-muted-foreground/70 shrink-0" />
                            )}
                            <p
                              className={cn(
                                "text-sm font-medium truncate flex-1 text-left leading-tight",
                                currentConversationId === conversation._id 
                                  ? "text-primary" 
                                  : "text-foreground/90 group-hover:text-foreground"
                              )}
                              title={conversation.title}
                            >
                              {truncateTitle(conversation.title, 33)}
                            </p>
                          </div>
                          <div className="flex items-center shrink-0">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted/60"
                                >
                                  <MoreHorizontal size={12} />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48 bg-popover/95 backdrop-blur-md border border-border/60 shadow-lg">
                                <DropdownMenuItem onClick={() => void handleTogglePin(conversation._id)}>
                                  {conversation.isPinned ? (<><PinOff className="mr-2 h-4 w-4" />Unpin</>) : (<><Pin className="mr-2 h-4 w-4" />Pin</>)}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleEdit(conversation)}><Edit2 className="mr-2 h-4 w-4" />Rename</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => void handleGenerateTitle(conversation._id)}><Sparkles className="mr-2 h-4 w-4" />Generate Title</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => void handleDuplicate(conversation._id)}><Copy className="mr-2 h-4 w-4" />Duplicate</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => void handleShare(conversation._id)}><Share2 className="mr-2 h-4 w-4" />Share</DropdownMenuItem>
                                <DropdownMenuItem onClick={() => void handleExport(conversation._id)}><Download className="mr-2 h-4 w-4" />Export</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => openDeleteDialog(conversation._id)} className="text-destructive focus:text-destructive"><Trash2 className="mr-2 h-4 w-4" />Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            ))}

            {groupedConversations.length === 0 && (
              <div className="text-center text-muted-foreground p-6 space-y-3">
                <div className="w-12 h-12 rounded-full bg-muted/40 flex items-center justify-center mx-auto">
                  <MessageSquare size={16} className="text-muted-foreground/60" />
                </div>
                <div className="space-y-1">
                  <p className="text-body-md">{searchQuery ? "No matching conversations" : "No conversations yet"}</p>
                  <p className="text-xs text-muted-foreground/70">{searchQuery ? "Try adjusting your search terms" : "Start a new chat to begin your conversation"}</p>
                </div>
              </div>
            )}

            {status === "CanLoadMore" && (
              <div className="py-3">
                <Button 
                  variant="ghost" 
                  onClick={() => loadMore(15)} 
                  className="justify-start h-9 text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-150 group w-full"
                >
                  <ChevronDown size={14} className="mr-2 group-hover:translate-y-0.5 transition-transform duration-150" />
                  Load more conversations
                </Button>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Delete Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Trash2 size={18} className="text-destructive" />
                Delete Conversation
              </DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete the conversation and all its messages.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={() => void handleDelete()}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Share Dialog */}
        <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Share2 size={18} className="text-primary" />
                Share Conversation
              </DialogTitle>
              <DialogDescription>
                Anyone with this link can view the conversation (read-only).
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="share-url">Share URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="share-url"
                    value={shareUrl}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => void navigator.clipboard.writeText(shareUrl)}
                  >
                    <Copy size={16} />
                  </Button>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => window.open(shareUrl, '_blank')}
                  >
                    <ExternalLink size={16} />
                  </Button>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShareDialogOpen(false)}>
                Close
              </Button>
              <Button 
                variant="destructive" 
                onClick={() => activeConversationId && void handleUnshare(activeConversationId)}
              >
                Revoke Share
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Export Dialog */}
        <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download size={18} className="text-primary" />
                Export Conversation
              </DialogTitle>
              <DialogDescription>
                Download the conversation as a JSON file that can be imported later.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {exportData ? (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Export includes {exportData.messages.length} messages and {exportData.branches.length} branches.
                  </p>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Ready to download:</p>
                    <p className="text-xs text-muted-foreground">
                      {exportData.conversation.title} • {new Date(exportData.exportedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto" />
                  <p className="text-sm text-muted-foreground mt-2">Preparing export...</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setExportDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={() => void handleDownloadExport()}
                disabled={!exportData}
              >
                <Download size={16} className="mr-2" />
                Download
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* User Profile - Bottom */}
        {user && (
          <div className="border-t border-border/30 bg-background/95 backdrop-blur-xl">
            <div className="flex items-center gap-3 p-4">
              {user.image ? (
                <img
                  src={user.image}
                  alt="Avatar"
                  className="w-10 h-10 rounded-full object-cover border border-border/50"
                />
              ) : (
                <div className="w-10 h-10 rounded-full bg-muted/60 flex items-center justify-center border border-border/50">
                  <User size={16} className="text-muted-foreground/80" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p
                    className={cn(
                      "text-sm font-semibold truncate text-foreground",
                      preferences?.hideUserInfo && "blur-[3px] select-none"
                    )}
                  >
                    {user.name ?? user.email}
                  </p>
                  {usage?.plan && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className={cn(
                          "px-2 py-0.5 text-[10px] font-bold uppercase tracking-wider rounded-full shrink-0 cursor-help transition-all duration-150 hover:scale-105",
                          usage.plan === "free" ? "bg-slate-100 text-slate-700 dark:bg-slate-800/80 dark:text-slate-300 border border-slate-200 dark:border-slate-700" :
                          usage.plan === "pro" ? "bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 dark:from-blue-900/60 dark:to-blue-800/40 dark:text-blue-200 border border-blue-200 dark:border-blue-700 shadow-sm" :
                          usage.plan === "ultra" ? "bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 dark:from-purple-900/60 dark:to-purple-800/40 dark:text-purple-200 border border-purple-200 dark:border-purple-700 shadow-sm" :
                          "bg-gradient-to-r from-amber-100 to-yellow-50 text-amber-800 dark:from-amber-900/60 dark:to-yellow-800/40 dark:text-amber-200 border border-amber-200 dark:border-amber-700 shadow-sm"
                        )}>
                          {usage.plan}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p className="font-medium">
                          {usage.plan === "free" ? "Free Plan" : 
                           usage.plan === "pro" ? "Pro Plan" :
                           usage.plan === "ultra" ? "Ultra Plan" : "Max Plan"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {usage.creditsUsed.toLocaleString()} / {usage.creditsLimit.toLocaleString()} credits used
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <p
                  className={cn(
                    "text-xs text-muted-foreground/80 truncate",
                    preferences?.hideUserInfo && "blur-[3px] select-none"
                  )}
                >
                  {user.email}
                </p>
              </div>
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => void toggleUserInfoVisibility()}
                      className="h-8 w-8 hover:bg-muted/60 transition-colors"
                      title={preferences?.hideUserInfo ? "Show user info" : "Hide user info"}
                    >
                      {preferences?.hideUserInfo ? <EyeOff size={14} /> : <Eye size={14} />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>{preferences?.hideUserInfo ? "Show user info" : "Hide user info"}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        )}

    </div>
    </TooltipProvider>
  );
});