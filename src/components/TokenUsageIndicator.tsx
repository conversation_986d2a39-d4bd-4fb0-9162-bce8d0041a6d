import { useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

/**
 * TokenUsageIndicator
 * Shows remaining usage credits in a circular progress ring.
 * On hover, displays detailed information about remaining and total credits.
 */
export default function TokenUsageIndicator() {
  const usage = useQuery(api.usage.get);

  const { percentUsed, usedCredits, remainingCredits } = useMemo(() => {
    if (!usage || usage.creditsLimit === 0) {
      return { percentUsed: 0, usedCredits: 0, remainingCredits: 0 };
    }
    const used = Math.min(usage.creditsUsed, usage.creditsLimit);
    const remaining = Math.max(usage.creditsLimit - used, 0);
    const percent = Math.min((used / usage.creditsLimit) * 100, 100);
    return { percentUsed: percent, usedCredits: used, remainingCredits: remaining };
  }, [usage]);

  if (usage === undefined) return null; // loading state handled by caller

  const size = 36; // svg size in px
  const strokeWidth = 4;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference * (1 - percentUsed / 100);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="relative h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center text-primary cursor-default select-none">
          <svg width={size} height={size} className="rotate-[-90deg]">
            {/* Track */}
            <circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              strokeWidth={strokeWidth}
              stroke="currentColor"
              className="text-primary/20"
              fill="transparent"
            />
            {/* Progress */}
            <circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              strokeWidth={strokeWidth}
              strokeLinecap="round"
              stroke="currentColor"
              className="text-primary"
              fill="transparent"
              strokeDasharray={`${circumference} ${circumference}`}
              strokeDashoffset={offset}
            />
          </svg>
          <span className="absolute text-[10px] font-medium leading-none text-foreground">
            {Math.round(percentUsed)}%
          </span>
        </div>
      </TooltipTrigger>
      <TooltipContent sideOffset={8} className="text-center">
        <p className="font-medium">{usedCredits} / {usage.creditsLimit} credits used</p>
        <p className="text-xs text-muted-foreground">{remainingCredits} credits left</p>
        <p className="text-xs text-muted-foreground">Plan: {usage.plan}</p>
      </TooltipContent>
    </Tooltip>
  );
} 