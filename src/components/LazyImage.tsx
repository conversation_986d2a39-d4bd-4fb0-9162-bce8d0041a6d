import React, { memo, useState, useCallback, useRef, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

// useIntersectionObserver hook implementation
function useIntersectionObserver<T extends Element>(
  options: {
    root?: Element | null;
    rootMargin?: string;
    threshold?: number | number[];
  } = {}
): [React.RefCallback<T>, boolean] {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const nodeRef = useRef<T | null>(null);

  const ref = useCallback((node: T | null) => {
    nodeRef.current = node;
  }, []);

  useEffect(() => {
    const node = nodeRef.current;
    if (!node) return;

    // Check if IntersectionObserver is supported
    if (!window.IntersectionObserver) {
      setIsIntersecting(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        root: options.root || null,
        rootMargin: options.rootMargin || '50px',
        threshold: options.threshold || 0.1,
      }
    );

    observer.observe(node);

    return () => {
      observer.disconnect();
    };
  }, [options.root, options.rootMargin, options.threshold]);

  return [ref, isIntersecting];
}

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  onClick?: () => void;
  width?: number;
  height?: number;
  style?: React.CSSProperties;
}

const LazyImage = memo<LazyImageProps>(({
  src,
  alt,
  className,
  onClick,
  width,
  height,
  style,
}) => {
  const [ref, isIntersecting] = useIntersectionObserver<HTMLDivElement>({
    rootMargin: '50px',
    threshold: 0.1,
  });
  
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);

  // Start loading when element intersects viewport
  useEffect(() => {
    if (isIntersecting && !shouldLoad) {
      setShouldLoad(true);
    }
  }, [isIntersecting, shouldLoad]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(false);
  }, []);

  const handleClick = useCallback(() => {
    if (onClick && (imageLoaded || imageError)) {
      onClick();
    }
  }, [onClick, imageLoaded, imageError]);

  // Calculate skeleton dimensions
  const skeletonStyle = {
    width: width || '100%',
    height: height || '200px',
    ...style,
  };

  return (
    <div
      ref={ref}
      className={cn(
        'relative overflow-hidden rounded-md',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={handleClick}
      style={style}
    >
      {!shouldLoad && (
        <Skeleton 
          className="w-full h-full min-h-[200px]" 
          style={skeletonStyle}
        />
      )}
      
      {shouldLoad && !imageLoaded && !imageError && (
        <Skeleton 
          className="w-full h-full min-h-[200px]" 
          style={skeletonStyle}
        />
      )}
      
      {shouldLoad && (
        <img
          src={src}
          alt={alt}
          loading="lazy"
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={cn(
            'transition-opacity duration-300',
            imageLoaded ? 'opacity-100' : 'opacity-0',
            imageError && 'hidden',
            'w-full h-auto object-cover'
          )}
          style={{
            position: imageLoaded ? 'static' : 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
          }}
        />
      )}
      
      {imageError && (
        <div 
          className="flex items-center justify-center bg-muted text-muted-foreground rounded-md"
          style={skeletonStyle}
        >
          <div className="flex flex-col items-center gap-2 p-4">
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span className="text-xs text-center">Failed to load image</span>
          </div>
        </div>
      )}
    </div>
  );
});

LazyImage.displayName = 'LazyImage';

export { LazyImage };