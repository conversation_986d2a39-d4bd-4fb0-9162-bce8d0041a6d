"use client"

import type React from "react"

import { useState, useRef, useEffect, forwardRef, memo, useMemo } from "react"
import { createPortal } from "react-dom"
import { useQuery, useMutation, useAction } from "convex/react"
import { api } from "../../convex/_generated/api"
import type { Id } from "../../convex/_generated/dataModel"
import {
  Send,
  Paperclip,
  Infinity as InfinityIcon,
  Wrench,
  Calculator,
  Clock,
  CloudRain,
  Zap,
  Bot,
  StopCircle,
  Link,
  BookOpen,
  Code,
  Database,
  Camera,
  User,
  Heart,
  Users,
  Smile,
  Stethoscope,
  MessageSquareHeart,
  MessageSquare,
  ListCollapse,
  Lightbulb,
  Mail,
  Sparkles,
  X,
  Binoculars,
  Brain,
  Search,
  MoreHorizontal,
  ChevronDown,
} from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { PROVIDER_CONFIGS, getModelInfo, getBestAvailableProvider } from "@/lib/models"
import { FileAttachment } from "./FileAttachment"
import { ModelSelector } from "./ModelSelector"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { PERSONAS } from "@/lib/personas"
import { RECIPES } from "@/lib/recipes"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// A simple hook to check for desktop screen sizes
const useIsDesktop = () => {
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 768)
  useEffect(() => {
    const handleResize = () => setIsDesktop(window.innerWidth >= 768)
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])
  return isDesktop
}

type AiProvider =
  | "openai"
  | "anthropic"
  | "google"
  | "openrouter"
  | "groq"
  | "deepseek"
  | "grok"
  | "cohere"
  | "mistral"
  | "cerebras"
  | "github"
  | "system"

// Define the valid tool types to match backend (including dynamic MCP tools)
type ValidTool = string

// Custom tool types
type CustomTool = "deep_search" | "agent" | null

export interface AttachmentData {
  type: "image" | "file"
  url: string
  name: string
  size?: number
  storageId?: Id<"_storage">
  extractedText?: string
  mimeType?: string
  isUploading?: boolean
  uploadProgress?: number
}

interface MessageInputProps {
  onSendMessage: (
    content: string,
    attachments?: AttachmentData[],
    selectedModel?: { provider: string; model: string; thinkingBudget?: string | number },
    enabledTools?: ValidTool[],
    persona?: string,
    recipe?: string,
  ) => void
  disabled?: boolean
  isGenerating?: boolean
  onStopGeneration?: () => void
}

const ChatTextarea = forwardRef<HTMLTextAreaElement, any>((props, ref) => {
  return (
    <Textarea
      ref={ref}
      {...props}
      rows={1}
      placeholder="Message ErzenAI..."
      className="flex-1 resize-none border-0 shadow-none focus-visible:ring-0 bg-transparent py-3 px-0 text-base placeholder:text-muted-foreground/60 min-h-[40px] max-h-40"
    />
  )
})

// Helper function to get tool icons
const getToolIcon = (toolId: string) => {
  // Handle MCP tools
  if (toolId.startsWith("mcp_")) {
    return Bot // Use Bot icon for MCP tools
  }

  switch (toolId) {
    case "web_search":
      return Search
    case "deep_search":
      return Zap
    case "weather":
      return CloudRain
    case "calculator":
      return Calculator
    case "datetime":
      return Clock
    case "thinking":
      return Brain
    case "memory":
      return Database
    case "url_fetch":
      return Link
    case "code_analysis":
      return Code
    case "image_generation":
      return Camera
    case "conversation_context":
      return BookOpen
    case "plan":
      return ListCollapse
    default:
      return Wrench
  }
}

// Helper function to get user-friendly thinking budget labels
const getThinkingBudgetLabel = (budget: string | number): string => {
  if (typeof budget === "string") {
    return budget.charAt(0).toUpperCase() + budget.slice(1)
  }

  // Map numeric values to friendly names
  switch (budget) {
    case 0:
      return "Off"
    case 1024:
      return "Low"
    case 2048:
      return "Low"
    case 4096:
      return "Medium"
    case 8192:
      return "High"
    case 16384:
      return "Very High"
    case 24576:
      return "Max"
    case 32768:
      return "Max"
    default:
      return `${budget} tokens`
  }
}

// Helper function to get thinking budget indicator
const getThinkingBudgetIndicator = (
  budget: string | number | undefined,
): { text: string; icon: React.ComponentType<{ size: number; className?: string }> } => {
  if (budget === undefined || budget === 0) {
    return { text: "", icon: Brain }
  }

  if (typeof budget === "string") {
    return { text: budget.charAt(0).toUpperCase(), icon: Brain }
  }

  // Map numeric values to short indicators and icons
  switch (budget) {
    case 1024:
      return { text: "L", icon: Brain }
    case 2048:
      return { text: "L", icon: Brain }
    case 4096:
      return { text: "M", icon: Brain }
    case 8192:
      return { text: "H", icon: Brain }
    case 16384:
      return { text: "VH", icon: Zap }
    case 24576:
      return { text: "MAX", icon: Zap }
    case 32768:
      return { text: "MAX", icon: Zap }
    default:
      return { text: "?", icon: Brain }
  }
}

// Custom Tools Selector Component
interface CustomToolsSelectorProps {
  selectedCustomTool: CustomTool
  onSelectCustomTool: (tool: CustomTool) => void
}

const CustomToolsSelector = ({ selectedCustomTool, onSelectCustomTool }: CustomToolsSelectorProps) => {
  return (
    <div className="flex items-center gap-2 mb-3">
      <div className="flex items-center gap-2">
        {/* Deep Search Tool */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant={selectedCustomTool === "deep_search" ? "default" : "ghost"}
                size="sm"
                onClick={() => onSelectCustomTool(selectedCustomTool === "deep_search" ? null : "deep_search")}
                className={cn(
                  "h-7 px-2.5 rounded-full text-xs font-medium gap-1 flex items-center",
                  selectedCustomTool === "deep_search"
                    ? "bg-primary text-primary-foreground shadow"
                    : "text-foreground hover:text-primary hover:bg-muted/50",
                )}
              >
                <Binoculars size={14} className="" />
                Research
                {selectedCustomTool === "deep_search" && <X size={12} className="opacity-70" />}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Advanced deep search</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Agent Tool */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant={selectedCustomTool === "agent" ? "default" : "ghost"}
                size="sm"
                onClick={() => onSelectCustomTool(selectedCustomTool === "agent" ? null : "agent")}
                className={cn(
                  "h-7 px-2.5 rounded-full text-xs font-medium gap-1 flex items-center",
                  selectedCustomTool === "agent"
                    ? "bg-primary text-primary-foreground shadow"
                    : "text-foreground hover:text-primary hover:bg-muted/50",
                )}
              >
                <InfinityIcon size={14} className="" />
                Agent
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Agent with computer control</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>


    </div>
  )
}

// ToolSelector Component
interface ToolSelectorProps {
  enabledTools: ValidTool[]
  availableTools: any[]
  modelSupportsTools: boolean
  modelDisplayName: string
  onToggleTool: (toolId: ValidTool) => void
  disabled?: boolean
}

const ToolSelector = ({
  enabledTools,
  availableTools,
  modelSupportsTools,
  modelDisplayName,
  onToggleTool,
  disabled,
}: ToolSelectorProps) => {
  // Keep logic but avoid rendering separate popover; will be used inside Options menu
  const categorized = useMemo(() => {
    const tools = { regular: [] as any[], mcp: [] as any[], n8n: [] as any[] }
    availableTools.forEach((tool) => {
      const id: string = tool.id || ""
      if (id.startsWith("mcp_")) tools.mcp.push(tool)
      else if (id.startsWith("n8n_")) {
        const segments = id.split("_")
        if (segments.length === 2) tools.n8n.push(tool)
      } else tools.regular.push(tool)
    })
    return tools
  }, [availableTools])

  const renderToolItems = (tools: any[]) =>
    tools.map((tool) => {
      const IconComponent = tool.icon
      const isEnabled = enabledTools.includes(tool.id)
      return (
        <DropdownMenuCheckboxItem
          key={tool.id}
          checked={isEnabled}
          onCheckedChange={() => onToggleTool(tool.id)}
          disabled={disabled || !modelSupportsTools}
          className="capitalize"
        >
          <div className="flex items-center gap-2">
            <span className={cn("p-1 rounded-sm", isEnabled ? "text-primary" : "text-muted-foreground")}> 
              <IconComponent size={14} />
            </span>
            <span className="truncate">{tool.name.startsWith("MCP: ") ? tool.name.replace(/^MCP: /, "") : tool.name}</span>
          </div>
        </DropdownMenuCheckboxItem>
      )
    })

  return (
    <>
      <DropdownMenuLabel>Tools</DropdownMenuLabel>
      <DropdownMenuSeparator />
      {categorized.regular.length > 0 && (
        <DropdownMenuGroup>
          {renderToolItems(categorized.regular)}
        </DropdownMenuGroup>
      )}
      {categorized.mcp.length > 0 && (
        <>
          <DropdownMenuSeparator />
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="flex items-center gap-2"><Bot size={14}/> MCP</DropdownMenuSubTrigger>
            <DropdownMenuSubContent>{renderToolItems(categorized.mcp)}</DropdownMenuSubContent>
          </DropdownMenuSub>
        </>
      )}
      {categorized.n8n.length > 0 && (
        <>
          <DropdownMenuSeparator />
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="flex items-center gap-2"><Zap size={14}/> n8n</DropdownMenuSubTrigger>
            <DropdownMenuSubContent>{renderToolItems(categorized.n8n)}</DropdownMenuSubContent>
          </DropdownMenuSub>
        </>
      )}
      {!modelSupportsTools && (
        <>
          <DropdownMenuSeparator />
          <DropdownMenuItem disabled>{modelDisplayName} doesn't support tools</DropdownMenuItem>
        </>
      )}
    </>
  )
}

export const MessageInput = memo(function MessageInput({
  onSendMessage,
  disabled,
  isGenerating,
  onStopGeneration,
}: MessageInputProps) {
  const [message, setMessage] = useState("")
  const [attachments, setAttachments] = useState<AttachmentData[]>([])
  const [selectedProvider, setSelectedProvider] = useState<AiProvider>("google")
  const [selectedModel, setSelectedModel] = useState("gemini-2.5-flash-preview-05-20")
  const [enabledTools, setEnabledTools] = useState<ValidTool[]>([])
  const [availableProviders, setAvailableProviders] = useState<string[]>([])
  const [isUploadingFile, setIsUploadingFile] = useState(false)
  const [isSavingPrefs, setIsSavingPrefs] = useState(false)
  const [selectedThinkingBudget, setSelectedThinkingBudget] = useState<string | number | undefined>(2048)
  const [availableTools, setAvailableTools] = useState<any[]>([])
  const [persona, setPersona] = useState("")
  const [recipe, setRecipe] = useState("")
  const [isPersonaRecipeMenuOpen, setIsPersonaRecipeMenuOpen] = useState(false)

  // New state for custom tools
  const [selectedCustomTool, setSelectedCustomTool] = useState<CustomTool>(null)

  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const _isDesktop = useIsDesktop()

  const preferences = useQuery(api.preferences.get)
  const updatePreferences = useMutation(api.preferences.update)
  const updateModelReasoningEffort = useMutation(api.preferences.updateModelReasoningEffort)
  const userApiKeysQuery = useQuery(api.apiKeys.list)
  const userApiKeys = useMemo(() => userApiKeysQuery || [], [userApiKeysQuery])
  const customProviders = useQuery(api.customProviders.list) || []
  const getAvailableProviders = useAction(api.ai.getAvailableProviders)
  const getAvailableTools = useAction(api.ai.getAvailableTools)
  const prefetchMCPTools = useAction(api.ai.prefetchMCPTools)
  const generateUploadUrl = useMutation(api.files.generateUploadUrl)
  const storeFileMetadata = useMutation(api.files.storeFileMetadata)
  const extractTextFromFile = useAction(api.files.extractTextFromFile)

  // Content moderation hook
  const moderateMessage = useAction(api.moderation.moderate)

  // Get current model info
  const currentModelInfo = getModelInfo(selectedModel)

  // Helper to check if a model should be shown in the UI
  const _isModelVisible = (modelId: string) => !getModelInfo(modelId).hidden

  const modelSupportsTools = currentModelInfo.supportsTools
  const modelIsMultimodal = currentModelInfo.isMultimodal
  const modelSupportsThinking = currentModelInfo.supportsThinking
  const modelThinkingBudgets = currentModelInfo.thinkingBudgets

  // Check if custom tools are active (disables other controls)
  const isCustomToolActive = selectedCustomTool !== null

  // Handle custom tool selection
  const handleCustomToolSelect = (tool: CustomTool) => {
    setSelectedCustomTool(tool)

    if (tool === "deep_search") {
      // Automatically select model based on user plan
      const plan = usage?.plan ?? "free"
      setSelectedProvider("openai")
      const deepSearchModel = plan === "ultra" || plan === "max" ? "o4-mini-deep-research" : "o4-mini-deep-research"
      setSelectedModel(deepSearchModel)
      // Clear other selections when using custom tools
      setEnabledTools([])
      setPersona("")
      setRecipe("")
    } else if (tool === "agent") {
      // Automatically select an agent-centric model 🔥
      setSelectedProvider("groq")
      // Prefer Moonshot Kimi K2 model for long-running agent tasks
      const agentModel = "qwen/qwen3-coder:free"
      setSelectedModel(agentModel)

      // Clear user-specific settings to avoid conflicts but set agent recipe
      setEnabledTools([])
      setPersona("")
      setRecipe("agent")
    } else if (tool === null) {
      // Reset to previous selections when deactivating custom tool
      if (preferences) {
        setSelectedProvider(preferences.aiProvider ?? "google")
        setSelectedModel(preferences.model ?? "gemini-2.5-flash-preview-05-20")
        setEnabledTools(preferences.enabledTools ?? [])
      }
    }
  }

  useEffect(() => {
    if (preferences) {
      // Only update if not using custom tools
      if (!isCustomToolActive) {
        setEnabledTools(preferences.enabledTools ?? [])
        setSelectedProvider(preferences.aiProvider ?? "google")
        setSelectedModel(preferences.model ?? "gemini-2.5-flash-preview-05-20")
      }
    }
  }, [preferences, isCustomToolActive])

  // Load saved reasoning effort for current model
  useEffect(() => {
    if (preferences && selectedModel && !isCustomToolActive) {
      const reasoningEfforts: Record<string, string | number> = preferences.modelReasoningEfforts || {}
      const savedReasoningEffort = reasoningEfforts[selectedModel]
      if (savedReasoningEffort !== undefined) {
        setSelectedThinkingBudget(savedReasoningEffort)
      } else {
        // Set default reasoning effort based on model type
        const modelInfo = getModelInfo(selectedModel)
        if (modelInfo.supportsThinking && modelInfo.thinkingBudgets) {
          if (Array.isArray(modelInfo.thinkingBudgets) && modelInfo.thinkingBudgets.length > 0) {
            const defaultBudget = modelInfo.thinkingBudgets[Math.floor(modelInfo.thinkingBudgets.length / 2)]
            setSelectedThinkingBudget(defaultBudget)
          }
        } else {
          setSelectedThinkingBudget(undefined)
        }
      }
    }
  }, [preferences, selectedModel, isCustomToolActive])

  // Load available providers (cached to avoid refetching on every render)
  const [providersLoaded, setProvidersLoaded] = useState(false)
  useEffect(() => {
    if (providersLoaded) return // Only load once
    const loadProviders = async () => {
      try {
        const providers = await getAvailableProviders()
        setAvailableProviders(providers)
        setProvidersLoaded(true)
      } catch (error) {
        console.error("Failed to load available providers:", error)
        const fallbackProviders = Object.keys(PROVIDER_CONFIGS).filter((provider) => {
          if (provider === "openai" || provider === "google") return true
          return userApiKeys.some((key) => key.provider === provider && key.hasKey)
        })
        setAvailableProviders(fallbackProviders)
        setProvidersLoaded(true)
      }
    }
    void loadProviders()
  }, [getAvailableProviders, userApiKeys, providersLoaded])

  // Refresh providers when userApiKeys or customProviders change
  useEffect(() => {
    if (providersLoaded) {
      setProvidersLoaded(false) // Force reload when dependencies change
    }
  }, [userApiKeys.length, customProviders.length])

  // Load available tools
  useEffect(() => {
    const loadTools = async () => {
      try {
        const toolsConfig = await getAvailableTools()
        const toolsArray = Object.values(toolsConfig).map((tool) => ({
          ...tool,
          icon: getToolIcon(tool.id),
        }))
        setAvailableTools(toolsArray)
        // If any MCP tools are present, fire off a prefetch in the background
        if (toolsArray.some((t) => t.id.startsWith("mcp_"))) {
          void prefetchMCPTools().catch((err) => {
            console.warn("MCP prefetch failed:", err)
          })
        }
      } catch (error) {
        console.error("Failed to load available tools:", error)
        setAvailableTools([])
      }
    }
    void loadTools()
  }, [getAvailableTools])

  // Clean up enabled tools when available tools change
  useEffect(() => {
    if (availableTools.length === 0 || isCustomToolActive) return

    const availableToolIds = availableTools.map((tool) => tool.id)
    const cleanedEnabledTools = enabledTools.filter((toolId) => availableToolIds.includes(toolId))

    // Update enabled tools if any were removed
    if (cleanedEnabledTools.length !== enabledTools.length) {
      setEnabledTools(cleanedEnabledTools)
      // Also update preferences to persist the cleanup
      if (preferences) {
        void updatePreferences({
          aiProvider: preferences.aiProvider,
          model: preferences.model,
          temperature: preferences.temperature,
          enabledTools: cleanedEnabledTools,
          favoriteModels: preferences.favoriteModels,
          hideUserInfo: preferences.hideUserInfo,
          showToolOutputs: preferences.showToolOutputs,
          showMessageMetadata: preferences.showMessageMetadata,
          showThinking: preferences.showThinking,
          systemPrompt: preferences.systemPrompt,
          useCustomSystemPrompt: preferences.useCustomSystemPrompt,
          imageModel: preferences.imageModel,
          theme: preferences.theme,
          colorTheme: preferences.colorTheme,
          modelReasoningEfforts: preferences.modelReasoningEfforts,
        })
      }
    }
  }, [availableTools, enabledTools, preferences, updatePreferences, isCustomToolActive])

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
    adjustTextareaHeight()
  }

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    const files = e.clipboardData.files
    if (files?.length > 0) {
      e.preventDefault()
      void handleFileUpload(files)
    }
  }

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return
    setIsUploadingFile(true)

    try {
      for (const file of Array.from(files)) {
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          toast.error("File Too Large", {
            description: `${file.name} is too large. Maximum size is 10MB.`,
          })
          continue
        }

        // Check file type support based on current model
        const supportedImageTypes = ["image/png", "image/jpeg", "image/jpg", "image/gif", "image/webp"]
        const supportedFileTypes = [
          "application/pdf",
          "text/plain",
          "text/csv",
          "application/json",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // docx
          "application/msword", // doc
          "application/rtf", // rtf
          "text/rtf", // rtf alternative mime
        ]

        // Additional supported code/text file extensions
        const supportedCodeTypes = [
          "text/javascript",
          "application/javascript",
          "text/typescript",
          "application/typescript",
          "text/x-python",
          "application/x-python-code",
          "text/x-java-source",
          "text/x-c",
          "text/x-c++",
          "text/x-php",
          "text/x-ruby",
          "text/x-go",
          "text/x-rust",
          "text/x-swift",
          "text/x-kotlin",
          "text/x-scala",
          "text/x-shell",
          "application/x-sh",
          "text/html",
          "application/xml",
          "text/xml",
          "text/css",
          "application/json",
          "text/yaml",
          "application/x-yaml",
        ]

        const isImage = supportedImageTypes.includes(file.type)
        const isFile =
          supportedFileTypes.includes(file.type) ||
          supportedCodeTypes.includes(file.type) ||
          file.name.toLowerCase().endsWith(".txt") ||
          file.name.toLowerCase().endsWith(".md") ||
          file.name.toLowerCase().endsWith(".markdown") ||
          file.name.toLowerCase().endsWith(".docx") ||
          file.name.toLowerCase().endsWith(".doc") ||
          file.name.toLowerCase().endsWith(".rtf") ||
          file.name.toLowerCase().endsWith(".js") ||
          file.name.toLowerCase().endsWith(".ts") ||
          file.name.toLowerCase().endsWith(".jsx") ||
          file.name.toLowerCase().endsWith(".tsx") ||
          file.name.toLowerCase().endsWith(".py") ||
          file.name.toLowerCase().endsWith(".java") ||
          file.name.toLowerCase().endsWith(".c") ||
          file.name.toLowerCase().endsWith(".cpp") ||
          file.name.toLowerCase().endsWith(".cc") ||
          file.name.toLowerCase().endsWith(".cxx") ||
          file.name.toLowerCase().endsWith(".h") ||
          file.name.toLowerCase().endsWith(".hpp") ||
          file.name.toLowerCase().endsWith(".php") ||
          file.name.toLowerCase().endsWith(".rb") ||
          file.name.toLowerCase().endsWith(".go") ||
          file.name.toLowerCase().endsWith(".rs") ||
          file.name.toLowerCase().endsWith(".swift") ||
          file.name.toLowerCase().endsWith(".kt") ||
          file.name.toLowerCase().endsWith(".scala") ||
          file.name.toLowerCase().endsWith(".sh") ||
          file.name.toLowerCase().endsWith(".bash") ||
          file.name.toLowerCase().endsWith(".zsh") ||
          file.name.toLowerCase().endsWith(".fish") ||
          file.name.toLowerCase().endsWith(".html") ||
          file.name.toLowerCase().endsWith(".htm") ||
          file.name.toLowerCase().endsWith(".xml") ||
          file.name.toLowerCase().endsWith(".css") ||
          file.name.toLowerCase().endsWith(".scss") ||
          file.name.toLowerCase().endsWith(".sass") ||
          file.name.toLowerCase().endsWith(".less") ||
          file.name.toLowerCase().endsWith(".json") ||
          file.name.toLowerCase().endsWith(".yaml") ||
          file.name.toLowerCase().endsWith(".yml") ||
          file.name.toLowerCase().endsWith(".toml") ||
          file.name.toLowerCase().endsWith(".ini") ||
          file.name.toLowerCase().endsWith(".cfg") ||
          file.name.toLowerCase().endsWith(".conf")

        if (!isImage && !isFile) {
          toast.warning("Unsupported File Type", {
            description: `${file.name} - Supported types: Images (PNG, JPEG, GIF, WebP), Text files, Markdown, Code files, PDFs, and Word documents.`,
          })
          continue
        }

        // Provide guidance about images with text-only models
        if (isImage && !modelIsMultimodal) {
          toast.info("Image Upload with Text Model", {
            description: `${currentModelInfo.displayName} doesn't support images directly. The content will be processed for text extraction where possible.`,
          })
        }

        // Get file URL for immediate display (especially for images)
        const fileUrl = URL.createObjectURL(file)

        // Create attachment with immediate preview - determine attachment type
        let attachmentType: "image" | "file" = "file"
        if (isImage) attachmentType = "image"

        const newAttachment: AttachmentData = {
          type: attachmentType,
          url: fileUrl,
          name: file.name,
          size: file.size,
          mimeType: file.type,
          isUploading: true,
          uploadProgress: 0,
        }

        // Add to attachments immediately so user sees the file right away
        setAttachments((prev) => [...prev, newAttachment])

        // Start the actual upload process
        try {
          // Generate upload URL
          const uploadUrl = await generateUploadUrl()

          // Update progress to 10%
          setAttachments((prev) => prev.map((att) => (att.url === fileUrl ? { ...att, uploadProgress: 10 } : att)))

          // Upload file to Convex storage
          const result = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          })

          if (!result.ok) {
            throw new Error(`Failed to upload ${file.name}`)
          }

          const { storageId } = await result.json()

          // Update progress to 50%
          setAttachments((prev) => prev.map((att) => (att.url === fileUrl ? { ...att, uploadProgress: 50 } : att)))

          // Extract text from file if it's not an image or if using a non-multimodal model
          let extractedText = ""
          if (!isImage || !modelIsMultimodal) {
            try {
              extractedText = await extractTextFromFile({
                storageId,
                fileType: file.type,
              })
            } catch (error) {
              console.error("Failed to extract text:", error)
              extractedText = `[Failed to extract text from ${file.name}]`
            }
          }

          // Update progress to 80%
          setAttachments((prev) => prev.map((att) => (att.url === fileUrl ? { ...att, uploadProgress: 80 } : att)))

          // Store file metadata
          await storeFileMetadata({
            storageId,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            extractedText,
          })

          // Update attachment with final data
          setAttachments((prev) =>
            prev.map((att) =>
              att.url === fileUrl
                ? {
                    ...att,
                    storageId,
                    extractedText,
                    mimeType: file.type, // Ensure mimeType is preserved
                    isUploading: false,
                    uploadProgress: 100,
                  }
                : att,
            ),
          )

          // Show success message with model-specific guidance
          if (isImage && modelIsMultimodal) {
            toast.success("Image uploaded successfully", {
              description: `${file.name} will be analyzed by the multimodal model.`,
            })
          } else if (isFile) {
            toast.success("File uploaded successfully", {
              description: `${file.name} - Content extracted and ready for analysis.`,
            })
          } else {
            toast.success("File uploaded successfully", {
              description: `${file.name} - Text content extracted for processing.`,
            })
          }
        } catch (uploadError) {
          console.error("Upload failed for file:", file.name, uploadError)
          // Remove the failed attachment
          setAttachments((prev) => prev.filter((att) => att.url !== fileUrl))
          toast.error("File Upload Failed", {
            description: `Failed to upload ${file.name}. Please check the file size and format.`,
          })
        }
      }
    } catch (error) {
      console.error("File upload failed:", error)
      toast.error("Upload Error", {
        description: "Failed to upload files. Please check your internet connection and try again.",
      })
    } finally {
      setIsUploadingFile(false)
    }
  }

  const handleFileDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = e.dataTransfer.files
    void handleFileUpload(files)
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Quick validation checks first
    if ((!message.trim() && attachments.length === 0) || disabled) {
      return
    }

    // Fire-and-forget moderation check so the UI isn't blocked
    void moderateMessage({ text: message })
      .then((flagged) => {
        if (flagged) {
          toast.error("Message violates policy and was stopped.")
          if (onStopGeneration) {
            onStopGeneration()
          }
        }
      })
      .catch((err) => {
        // Log but do not block user flow
        console.error("Moderation error (non-blocking):", err)
      })

    // Check if any attachments are still uploading
    const hasUploadingAttachments = attachments.some((att) => att.isUploading)
    if (hasUploadingAttachments) {
      toast.warning("Please wait", {
        description: "Files are still uploading. Please wait for upload to complete.",
      })
      return
    }

    // Check if using non-multimodal model with images (only for validation, not blocking)
    const hasImages = attachments.some((att) => att.type === "image")
    if (hasImages && !modelIsMultimodal && !isCustomToolActive) {
      toast.warning("Model Doesn't Support Images", {
        description: `${currentModelInfo.displayName} doesn't support images directly. Please choose a multimodal model or remove image attachments.`,
      })
      return
    }

    // Immediately clear UI state for faster UX
    const currentMessage = message
    const currentAttachments = attachments.length > 0 ? [...attachments] : undefined

    setMessage("")
    setAttachments([])
    if (textareaRef.current) textareaRef.current.style.height = "auto"

    // Determine tools to send based on custom tool selection
    let toolsToSend: ValidTool[] = []
    if (selectedCustomTool === "deep_search") {
      // Deep research models require additional built-in tools.
      // Only use OpenAI's built-in tools for deep research models.
      toolsToSend = [
        "web_search_preview", // OpenAI built-in browser tool
        "code_interpreter",   // Enable built-in code interpreter for analysis
      ]
    } else if (selectedCustomTool === "agent") {
      // Agent mode – give the model a rich toolbox 🧰
      toolsToSend = [
        "plan",
        "thinking",
        "web_search",
        "deep_search",
        "calculator",
        "datetime",
        "weather",
        "url_fetch",
        "code_analysis",
        "memory",
        "image_generation",
        "conversation_context",
      ]
    } else if (!isCustomToolActive && modelSupportsTools) {
      toolsToSend = enabledTools
    }

    // Determine persona/recipe arguments based on custom tool modes
    const personaArg = !isCustomToolActive ? persona : "";
    const recipeArg = selectedCustomTool === "agent" ? recipe : (!isCustomToolActive ? recipe : "");

    onSendMessage(
      currentMessage,
      currentAttachments,
      {
        provider: selectedProvider,
        model: selectedModel,
        thinkingBudget: modelSupportsThinking ? selectedThinkingBudget : undefined,
      },
      toolsToSend,
      personaArg,
      recipeArg,
    )
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      // Use setTimeout to ensure DOM updates happen immediately
      setTimeout(() => void handleSubmit(e as any), 0)
    }
  }

  const handleModelSelect = (provider: string, model: string) => {
    if (isCustomToolActive) return // Prevent model changes when custom tool is active

    // Use getBestAvailableProvider to select the optimal provider for the chosen model
    const bestProvider = getBestAvailableProvider(model, availableProviders)
    const finalProvider = bestProvider ? bestProvider.provider : provider
    const finalModel = bestProvider ? bestProvider.modelId : model

    const typedProvider = finalProvider as AiProvider
    setSelectedProvider(typedProvider)
    setSelectedModel(finalModel)
    if (preferences) {
      void updatePreferences({
        aiProvider: typedProvider,
        model: finalModel,
        temperature: preferences.temperature,
        enabledTools: preferences.enabledTools,
        favoriteModels: preferences.favoriteModels,
        hideUserInfo: preferences.hideUserInfo,
        showToolOutputs: preferences.showToolOutputs,
        showMessageMetadata: preferences.showMessageMetadata,
        showThinking: preferences.showThinking,
        systemPrompt: preferences.systemPrompt,
        useCustomSystemPrompt: preferences.useCustomSystemPrompt,
        theme: preferences.theme,
        colorTheme: preferences.colorTheme,
      })
    }
  }

  const handleToolsChange = async (tools: ValidTool[]) => {
    if (isCustomToolActive) return // Prevent tool changes when custom tool is active

    setEnabledTools(tools)
    if (preferences) {
      try {
        setIsSavingPrefs(true)
        await updatePreferences({
          aiProvider: preferences.aiProvider,
          model: preferences.model,
          temperature: preferences.temperature,
          enabledTools: tools,
          favoriteModels: preferences.favoriteModels,
          hideUserInfo: preferences.hideUserInfo,
          showToolOutputs: preferences.showToolOutputs,
          showMessageMetadata: preferences.showMessageMetadata,
          showThinking: preferences.showThinking,
          systemPrompt: preferences.systemPrompt,
          useCustomSystemPrompt: preferences.useCustomSystemPrompt,
          imageModel: preferences.imageModel,
          theme: preferences.theme,
          colorTheme: preferences.colorTheme,
          modelReasoningEfforts: preferences.modelReasoningEfforts,
        })
      } finally {
        setIsSavingPrefs(false)
      }
    }
  }

  /* Toggle a tool on/off, persisting the preference */
  const handleToggleTool = (toolId: ValidTool) => {
    if (isCustomToolActive) return // Prevent tool changes when custom tool is active

    const newTools = enabledTools.includes(toolId)
      ? enabledTools.filter((t) => t !== toolId)
      : [...enabledTools, toolId]
    void handleToolsChange(newTools)
  }

  const handleReasoningEffortChange = async (budget: string | number) => {
    if (isCustomToolActive) return // Prevent changes when custom tool is active

    setSelectedThinkingBudget(budget)
    await updateModelReasoningEffort({
      model: selectedModel,
      reasoningEffort: budget,
    })
  }

  // Current usage info to determine subscription plan
  const usage = useQuery(api.usage.get)

  return (
    <div
      className="pointer-events-auto w-full max-w-3xl mx-auto px-3 sm:px-4"
      onDrop={handleFileDrop}
      onDragOver={(e) => e.preventDefault()}
    >
      <form onSubmit={(e) => void handleSubmit(e)} className="relative">
        {/* Softer container with minimal chrome */}
        <div className="flex flex-col gap-3 rounded-xl border border-border/60 bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/75 shadow-sm p-3 sm:p-4 mx-auto relative overflow-hidden">
          {/* Attachment preview */}
          {attachments.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 pb-2 max-h-44 overflow-y-auto pr-1">
              {attachments.map((attachment, index) => (
                <FileAttachment
                  key={index}
                  type={attachment.type}
                  name={attachment.name}
                  url={attachment.url}
                  size={attachment.size}
                  extractedText={attachment.extractedText}
                  isUploading={attachment.isUploading}
                  uploadProgress={attachment.uploadProgress}
                  showRemove={true}
                  onRemove={() => removeAttachment(index)}
                  className="w-full"
                />
              ))}
            </div>
          )}

          {/* Input row */}
          <div className="flex items-end gap-2">
            {/* Leading actions */}
            <div className="flex items-center gap-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-md"
                      onClick={handleFileSelect}
                      disabled={isUploadingFile}
                    >
                      <Paperclip size={16} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>{isUploadingFile ? "Uploading..." : "Attach files"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Model - compact, hidden on very small screens */}
              {!isCustomToolActive && (
                <div className="hidden sm:block">
                  <ModelSelector
                    selectedProvider={selectedProvider}
                    selectedModel={selectedModel}
                    onModelSelect={handleModelSelect}
                    disabled={isCustomToolActive}
                  />
                </div>
              )}
            </div>

            {/* Textarea */}
            <div className="flex-1 min-w-0">
              <ChatTextarea
                ref={textareaRef}
                value={message}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onPaste={handlePaste}
              />
            </div>

            {/* Trailing actions */}
            <div className="flex items-center gap-1">
              {/* Consolidated Options menu */}
              <DropdownMenu>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuTrigger asChild>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-8 w-8 rounded-md",
                            (persona || recipe || (enabledTools?.length ?? 0) > 0 || (selectedThinkingBudget && selectedThinkingBudget !== 0))
                              ? "text-primary"
                              : "text-muted-foreground"
                          )}
                          disabled={disabled}
                        >
                          <MoreHorizontal size={16} />
                        </Button>
                      </DropdownMenuTrigger>
                    </TooltipTrigger>
                    <TooltipContent side="top">Options</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DropdownMenuContent align="end" className="w-72">
                  {/* Personas & Recipes */}
                  {!isCustomToolActive && (
                    <>
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger className="flex items-center gap-2"><Sparkles size={14}/> Persona</DropdownMenuSubTrigger>
                        <DropdownMenuSubContent className="w-80">
                          <div className="p-2 space-y-1">
                            {PERSONAS.map((p) => {
                              const Icon = ({
                                none: User,
                                companion: Heart,
                                friend: Users,
                                comedian: Smile,
                                not_a_doctor: Stethoscope,
                                not_a_therapist: MessageSquareHeart,
                              } as const)[p.id] ?? Bot
                              return (
                                <button
                                  key={p.id}
                                  onClick={() => {
                                    if (!isCustomToolActive) {
                                      setPersona(p.id === "none" ? "" : p.id)
                                      if (p.id !== "none") setRecipe("")
                                    }
                                  }}
                                  className={cn(
                                    "w-full text-left p-2 hover:bg-muted/50 rounded-md flex items-start gap-3 transition-colors",
                                    persona === p.id ? "bg-primary/10 ring-1 ring-primary/30" : ""
                                  )}
                                >
                                  <span className={cn("p-2 rounded", persona === p.id ? "bg-primary/20 text-primary" : "bg-muted text-muted-foreground")}>{/* icon */}
                                    <Icon size={16} />
                                  </span>
                                  <div className="min-w-0">
                                    <div className={cn("font-medium", persona === p.id ? "text-primary" : "")}>{p.name}</div>
                                    <p className="text-xs text-muted-foreground truncate">{p.description}</p>
                                  </div>
                                </button>
                              )
                            })}
                          </div>
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>

                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger className="flex items-center gap-2"><Lightbulb size={14}/> Recipe</DropdownMenuSubTrigger>
                        <DropdownMenuSubContent className="w-80">
                          <div className="p-2 space-y-1">
                            {RECIPES.map((r) => {
                              const Icon = ({
                                none: MessageSquare,
                                summarise: ListCollapse,
                                brainstorm: Lightbulb,
                                email_draft: Mail,
                                code_review: Code,
                                learning_plan: BookOpen,
                                problem_solver: Brain,
                              } as const)[r.id] ?? Bot
                              return (
                                <button
                                  key={r.id}
                                  onClick={() => {
                                    if (!isCustomToolActive) {
                                      setRecipe(r.id === "none" ? "" : r.id)
                                      if (r.id !== "none") setPersona("")
                                    }
                                  }}
                                  className={cn(
                                    "w-full text-left p-2 hover:bg-muted/50 rounded-md flex items-start gap-3 transition-colors",
                                    recipe === r.id ? "bg-primary/10 ring-1 ring-primary/30" : ""
                                  )}
                                >
                                  <span className={cn("p-2 rounded", recipe === r.id ? "bg-primary/20 text-primary" : "bg-muted text-muted-foreground")}>
                                    <Icon size={16} />
                                  </span>
                                  <div className="min-w-0">
                                    <div className={cn("font-medium", recipe === r.id ? "text-primary" : "")}>{r.name}</div>
                                    <p className="text-xs text-muted-foreground truncate">{r.description}</p>
                                  </div>
                                </button>
                              )
                            })}
                          </div>
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>

                      <DropdownMenuSeparator />
                    </>
                  )}

                  {/* Tools */}
                  {!isCustomToolActive && (
                    <ToolSelector
                      enabledTools={enabledTools}
                      availableTools={availableTools}
                      modelSupportsTools={modelSupportsTools}
                      modelDisplayName={currentModelInfo.displayName}
                      onToggleTool={handleToggleTool}
                      disabled={false}
                    />
                  )}

                  {/* Thinking Effort */}
                  {modelSupportsThinking && modelThinkingBudgets && modelThinkingBudgets.length > 0 && !isCustomToolActive && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger className="flex items-center gap-2"><Brain size={14}/> Reasoning Effort</DropdownMenuSubTrigger>
                        <DropdownMenuSubContent className="w-56">
                          {modelThinkingBudgets.map((budget) => (
                            <DropdownMenuCheckboxItem
                              key={budget}
                              checked={selectedThinkingBudget === budget}
                              onCheckedChange={() => void handleReasoningEffortChange(budget)}
                            >
                              {getThinkingBudgetLabel(budget)}
                            </DropdownMenuCheckboxItem>
                          ))}
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Send/Stop */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type={isGenerating ? "button" : "submit"}
                      size="icon"
                      onClick={isGenerating ? onStopGeneration : undefined}
                      disabled={!isGenerating && (disabled || (!message.trim() && attachments.length === 0) || isUploadingFile || attachments.some((att) => att.isUploading) || isSavingPrefs)}
                      className={cn(
                        "h-9 w-9 rounded-md transition-colors",
                        isGenerating
                          ? "bg-destructive hover:bg-destructive/90 text-destructive-foreground"
                          : (message.trim() || attachments.length > 0) && !attachments.some((att) => att.isUploading)
                            ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                            : "bg-muted text-muted-foreground cursor-not-allowed"
                      )}
                    >
                      {isGenerating ? <StopCircle size={18} /> : <Send size={16} />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>{isGenerating ? "Stop generation" : attachments.some((att) => att.isUploading) ? "Uploading files..." : "Send message"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Subtle footer indicators */}
          <div className="flex items-center justify-between text-xs text-muted-foreground/80">
            <div className="flex items-center gap-2">
              {isCustomToolActive && (
                <span className="inline-flex items-center gap-1"><InfinityIcon size={12}/> Custom tool active</span>
              )}
              {enabledTools.length > 0 && !isCustomToolActive && (
                <span className="inline-flex items-center gap-1"><Wrench size={12}/> {enabledTools.length} tools</span>
              )}
              {selectedThinkingBudget !== undefined && selectedThinkingBudget !== 0 && modelSupportsThinking && !isCustomToolActive && (
                <span className="inline-flex items-center gap-1"><Brain size={12}/> {getThinkingBudgetLabel(selectedThinkingBudget)}</span>
              )}
            </div>
            {!isCustomToolActive && (
              <div className="sm:hidden text-muted-foreground/70">Model: {getModelInfo(selectedModel).displayName}</div>
            )}
          </div>
        </div>
        
        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={(e) => {
            if (e.target.files) {
              void handleFileUpload(e.target.files)
              e.target.value = '' // Reset input to allow selecting the same file again
            }
          }}
          accept=".png,.jpg,.jpeg,.gif,.webp,.pdf,.txt,.md,.markdown,.docx,.doc,.rtf,.js,.ts,.jsx,.tsx,.py,.java,.c,.cpp,.cc,.cxx,.h,.hpp,.php,.rb,.go,.rs,.swift,.kt,.scala,.sh,.bash,.zsh,.fish,.html,.htm,.xml,.css,.scss,.sass,.less,.json,.yaml,.yml,.toml,.ini,.cfg,.conf"
          className="hidden"
        />
      </form>
    </div>
  )
})
