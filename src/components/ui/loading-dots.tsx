import { cn } from "@/lib/utils"

interface LoadingDotsProps {
  className?: string
  size?: "xs" | "sm" | "md" | "lg"
  variant?: "bounce" | "pulse" | "wave" | "fade"
  color?: "primary" | "muted" | "accent"
}

export function LoadingDots({ 
  className, 
  size = "md", 
  variant = "wave",
  color = "primary" 
}: LoadingDotsProps) {
  const sizeClasses = {
    xs: "w-0.5 h-0.5",
    sm: "w-1 h-1",
    md: "w-1.5 h-1.5", 
    lg: "w-2 h-2"
  }

  const spacingClasses = {
    xs: "space-x-0.5",
    sm: "space-x-1",
    md: "space-x-1.5", 
    lg: "space-x-2"
  }

  const colorClasses = {
    primary: "bg-gradient-to-br from-primary via-primary to-primary/80",
    muted: "bg-gradient-to-br from-muted-foreground/60 to-muted-foreground/40",
    accent: "bg-gradient-to-br from-accent to-accent/80"
  }

  const dotSize = sizeClasses[size]
  const spacing = spacingClasses[size]
  const dotColor = colorClasses[color]

  // Enhanced animation styles using custom keyframes
  const getAnimationStyle = (index: number) => {
    const baseDelay = index * 150
    
    const animations = {
      bounce: {
        animation: `typing-bounce-smooth 1.4s infinite cubic-bezier(0.68, -0.55, 0.265, 1.55)`,
        animationDelay: `${baseDelay}ms`,
        transform: 'translateZ(0)' // Hardware acceleration
      },
      pulse: {
        animation: `typing-pulse 1.5s infinite ease-in-out`,
        animationDelay: `${baseDelay}ms`,
        transform: 'translateZ(0)'
      },
      wave: {
        animation: `typing-wave 1.6s infinite ease-in-out`,
        animationDelay: `${baseDelay}ms`,
        transform: 'translateZ(0)'
      },
      fade: {
        animation: `typing-fade 1.2s infinite ease-in-out`,
        animationDelay: `${baseDelay}ms`
      }
    }
    
    return animations[variant] || animations.bounce
  }

  const getExtraClasses = () => {
    switch (variant) {
      case "wave":
        return "shadow-sm hover:shadow-md transition-shadow duration-200"
      case "pulse":
        return "shadow-sm border border-primary/10"
      case "bounce":
        return "shadow-sm"
      case "fade":
        return "opacity-60"
      default:
        return "shadow-sm"
    }
  }

  return (
    <div 
      className={cn(
        "inline-flex items-center ml-1.5", 
        spacing, 
        className
      )}
      role="status" 
      aria-label="AI is typing"
    >
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className={cn(
            "rounded-full",
            dotSize,
            dotColor,
            getExtraClasses(),
            // Enhanced visual effects based on variant
            variant === "wave" && "transform-gpu",
            variant === "pulse" && "scale-100 hover:scale-110 transition-transform duration-200",
            // Better visual depth
            "ring-1 ring-black/5 dark:ring-white/5"
          )}
          style={getAnimationStyle(index)}
        />
      ))}
      
      {/* Optional text indicator for screen readers */}
      <span className="sr-only">AI is generating a response...</span>
    </div>
  )
} 