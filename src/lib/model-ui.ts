import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>Docker, Ai<PERSON>utlineOpenAI } from "react-icons/ai";
import { Ri<PERSON>laudeFill } from "react-icons/ri";
import { SiGooglegemini, SiX } from "react-icons/si";
import { TbB<PERSON>, TbRoute, TbWind } from "react-icons/tb";
import { HiLightningBolt } from "react-icons/hi";
import { <PERSON><PERSON>, Cpu, Settings } from "lucide-react";

/**
 * Maps company/group names to their corresponding React icon components
 */
export const getCompanyIcon = (
  groupName: string
): React.ComponentType<{ size?: number }> => {
  switch (groupName) {
    case "OpenAI":
      return AiOutlineOpenAI;
    case "Google":
      return SiGooglegemini;
    case "Anthropic":
      return RiClaudeFill;
    case "OpenRouter":
      return TbRoute;
    case "Groq":
      return HiLightningBolt;
    case "DeepSeek":
      return AiOutlineDocker;
    case "Grok":
      return SiX;
    case "Cohere":
      return TbBolt;
    case "Mistral":
      return TbWind;
    case "Cerebras":
      return Cpu;
    case "GitHub":
      return Bot;
    case "ErzenAI":
      return Settings;
    case "system":
      return Settings;
    default:
      return Bot;
  }
};

/**
 * Maps provider keys to their corresponding React icon components
 * Handles custom providers by checking against the provided custom providers array
 */
export const getProviderIcon = (
  provider: string,
  customProviders: Array<{ name: string }> = []
): React.ComponentType<{ size?: number }> => {
  // Check if this is a custom provider first
  const customProvider = customProviders.find((cp) => cp.name === provider);
  if (customProvider) {
    // For custom providers, we'll return a generic Bot icon since we can't render emoji icons as React components
    return Bot;
  }

  switch (provider) {
    case "openai":
      return AiOutlineOpenAI;
    case "google":
      return SiGooglegemini;
    case "anthropic":
      return RiClaudeFill;
    case "openrouter":
      return TbRoute;
    case "groq":
      return HiLightningBolt;
    case "deepseek":
      return AiOutlineDocker;
    case "grok":
      return SiX;
    case "cohere":
      return TbBolt;
    case "mistral":
      return TbWind;
    case "cerebras":
      return Cpu;
    case "github":
      return Bot;
    case "system":
      return Settings;
    default:
      return Bot;
  }
};
