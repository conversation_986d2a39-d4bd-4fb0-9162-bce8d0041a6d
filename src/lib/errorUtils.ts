/**
 * Minimal error utility for user-friendly error messages
 */

interface ErrorSummary {
  message: string;
  action?: string;
}

// Map of error patterns to minimal messages
const ERROR_PATTERNS: Array<{
  pattern: RegExp;
  message: string;
  action?: string;
}> = [
  // Authentication errors
  {
    pattern: /401|unauthorized|invalid.*api.*key|authentication/i,
    message: "Invalid API key",
    action: "Check settings",
  },

  // Permission errors
  {
    pattern: /403|forbidden|permission/i,
    message: "Access denied",
    action: "Check API key",
  },

  // Rate limiting
  {
    pattern: /429|rate.*limit|too.*many.*requests/i,
    message: "Too many requests",
    action: "Wait a moment",
  },

  // Quota/billing
  {
    pattern: /quota|billing|usage.*limit|exceeded.*limit/i,
    message: "Quota exceeded",
    action: "Check billing",
  },

  // Timeout errors
  {
    pattern: /timeout|timed.*out|connection.*timeout/i,
    message: "Request timeout",
    action: "Try again",
  },

  // Model errors
  {
    pattern: /model.*not.*found|model.*unavailable|no.*such.*model/i,
    message: "Model unavailable",
    action: "Switch model",
  },

  // Network errors
  {
    pattern: /network|connection|fetch.*failed|econnreset/i,
    message: "Connection error",
    action: "Check internet",
  },

  // Server errors
  {
    pattern: /500|502|503|internal.*server|bad.*gateway|service.*unavailable/i,
    message: "Service unavailable",
    action: "Try again later",
  },
];

/**
 * Convert technical error messages to minimal user-friendly messages
 */
export function getMinimalError(error: string | Error | unknown): ErrorSummary {
  const errorText =
    typeof error === "string"
      ? error
      : error instanceof Error
        ? error.message
        : String(error);

  // Find the first matching pattern
  for (const { pattern, message, action } of ERROR_PATTERNS) {
    if (pattern.test(errorText)) {
      return { message, action };
    }
  }

  // Default for unknown errors
  return { message: "Something went wrong", action: "Try again" };
}

/**
 * Combine multiple similar errors into a single message
 */
export function combineErrors(
  errors: Array<string | Error | unknown>
): ErrorSummary {
  if (errors.length === 0) {
    return { message: "Unknown error", action: "Try again" };
  }

  if (errors.length === 1) {
    return getMinimalError(errors[0]);
  }

  // Group errors by type
  const errorGroups = new Map<string, number>();
  let primaryError: ErrorSummary | null = null;

  for (const error of errors) {
    const summary = getMinimalError(error);
    const count = errorGroups.get(summary.message) || 0;
    errorGroups.set(summary.message, count + 1);

    if (!primaryError) {
      primaryError = summary;
    }
  }

  // If all errors are the same type, just return that with count
  if (errorGroups.size === 1 && primaryError) {
    const count = errors.length;
    return {
      message:
        count > 1
          ? `${primaryError.message} (${count}x)`
          : primaryError.message,
      action: primaryError.action,
    };
  }

  // If multiple error types, show count of issues
  return {
    message: `Multiple issues (${errors.length})`,
    action: "Check details",
  };
}

/**
 * Get a very short error message (single word or phrase)
 */
export function getUltraMinimalError(error: string | Error | unknown): string {
  const errorText =
    typeof error === "string"
      ? error
      : error instanceof Error
        ? error.message
        : String(error);

  if (/401|unauthorized|invalid.*api.*key|authentication/i.test(errorText))
    return "Auth error";
  if (/403|forbidden|permission/i.test(errorText)) return "Access denied";
  if (/429|rate.*limit/i.test(errorText)) return "Rate limited";
  if (/quota|billing|usage.*limit/i.test(errorText)) return "Quota exceeded";
  if (/timeout|timed.*out/i.test(errorText)) return "Timeout";
  if (/model.*not.*found|model.*unavailable/i.test(errorText))
    return "Model error";
  if (/network|connection|fetch.*failed/i.test(errorText))
    return "Network error";
  if (/500|502|503|internal.*server/i.test(errorText)) return "Service error";

  return "Error";
}

/**
 * Format error for toast notifications - even more minimal
 */
export function getToastError(error: string | Error | unknown): {
  title: string;
  description: string;
} {
  const summary = getMinimalError(error);
  return {
    title: summary.message,
    description: summary.action || "",
  };
}
