import { ImageModelId } from "./models";

export const PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest",
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo",
    ],
    keyPlaceholder: "sk-...",
    description: "GPT models from OpenAI",
    hasBuiltIn: true,
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite",
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash",
    ],
    keyPlaceholder: "AIza...",
    description: "Gemini models from Google",
    hasBuiltIn: true,
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229",
    ],
    keyPlaceholder: "sk-ant-...",
    description: "Claude models from Anthropic",
    hasBuiltIn: true,
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    keyPlaceholder: "sk-or-...",
    description: "Access to multiple AI models",
    hasBuiltIn: true,
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    keyPlaceholder: "gsk_...",
    description: "Ultra-fast inference",
    hasBuiltIn: true,
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    keyPlaceholder: "sk-...",
    description: "Reasoning and coding models",
    hasBuiltIn: true,
  },
  grok: {
    name: "Grok (xAI)",
    models: ["grok-beta", "grok-vision-beta"],
    keyPlaceholder: "xai-...",
    description: "Elon's AI with real-time data",
    hasBuiltIn: true,
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    keyPlaceholder: "co_...",
    description: "Enterprise-grade language models",
    hasBuiltIn: true,
  },
  mistral: {
    name: "Mistral AI",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest",
      "codestral-latest",
    ],
    keyPlaceholder: "...",
    description: "European AI models",
    hasBuiltIn: true,
  },
  tavily: {
    name: "Tavily Search",
    models: [],
    keyPlaceholder: "tvly-...",
    description: "Real-time web search API",
    hasBuiltIn: true,
  },
  openweather: {
    name: "OpenWeatherMap",
    models: [],
    keyPlaceholder: "...",
    description: "Weather data API",
    hasBuiltIn: true,
  },
  firecrawl: {
    name: "Firecrawl",
    models: [],
    keyPlaceholder: "fc-...",
    description: "AI-ready web scraping and crawling",
    hasBuiltIn: true,
  },
};

export const COLOR_THEMES = [
  {
    name: "Default",
    value: "default",
    colors: ["bg-slate-600", "bg-slate-500"],
  },
  { name: "Blue", value: "theme-blue", colors: ["bg-blue-600", "bg-blue-500"] },
  {
    name: "Green",
    value: "theme-green",
    colors: ["bg-green-600", "bg-green-500"],
  },
  {
    name: "Purple",
    value: "theme-purple",
    colors: ["bg-purple-600", "bg-purple-500"],
  },
  {
    name: "Orange",
    value: "theme-orange",
    colors: ["bg-orange-600", "bg-orange-500"],
  },
  { name: "Pink", value: "theme-pink", colors: ["bg-pink-600", "bg-pink-500"] },
  { name: "Teal", value: "theme-teal", colors: ["bg-teal-600", "bg-teal-500"] },
  { name: "Red", value: "theme-red", colors: ["bg-red-600", "bg-red-500"] },
  {
    name: "Indigo",
    value: "theme-indigo",
    colors: ["bg-indigo-600", "bg-indigo-500"],
  },
  {
    name: "Sunset",
    value: "theme-sunset",
    colors: ["bg-orange-500", "bg-pink-500"],
  },
  {
    name: "Ocean",
    value: "theme-ocean",
    colors: ["bg-cyan-600", "bg-sky-500"],
  },
  {
    name: "Forest",
    value: "theme-forest",
    colors: ["bg-emerald-700", "bg-lime-600"],
  },
  {
    name: "Gold",
    value: "theme-gold",
    colors: ["bg-amber-500", "bg-yellow-400"],
  },
];

export const UI_THEMES = [
  { name: "Default", value: "ui-default", preview: "rounded-md border" },
  { name: "Rounded", value: "ui-rounded", preview: "rounded-2xl border" },
  { name: "Square", value: "ui-square", preview: "rounded-sm border" },
  {
    name: "Glassmorphism",
    value: "ui-glass",
    preview: "backdrop-blur-sm border",
  },
];

export interface UserPreferences {
  aiProvider:
    | "openai"
    | "anthropic"
    | "google"
    | "openrouter"
    | "groq"
    | "deepseek"
    | "grok"
    | "cohere"
    | "mistral"
    | "cerebras"
    | "system";
  model: string;
  temperature: number;
  enabledTools?: string[];
  favoriteModels?: Array<{
    provider: string;
    model: string;
  }>;
  hideUserInfo?: boolean;
  showToolOutputs?: boolean;
  showMessageMetadata?: boolean;
  showThinking?: boolean;
  systemPrompt?: string;
  useCustomSystemPrompt?: boolean;
  imageModel?: ImageModelId;
  theme?: "light" | "dark" | "system";
  colorTheme?: string;
  uiTheme?: string;
}
